import React, { useState, useEffect } from "react";
import ReactD<PERSON> from "react-dom";
import { IoCloseCircleOutline } from "react-icons/io5";
import { CaretLeft, CaretRight, Plus, Trash } from "@phosphor-icons/react";
import WorkingHoursTimePicker from "./WorkingHoursTimePicker";
import DurationSelector from "./DurationSelector";

interface TimeSlot {
  startTime: string;
  endTime: string;
  duration?: number;
}

interface DatePickerModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (date: string, timeSlots: TimeSlot[], specificDayId?: string) => void;
  editDate?: string;
  editTimeSlots?: TimeSlot[];
  specificDayId?: string; // ID of the specific day being edited
}

const months = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
];

// Available durations in minutes - keep in sync with DurationSelector component
const VALID_DURATIONS = [15, 45, 50, 60];

const DatePickerModal: React.FC<DatePickerModalProps> = ({
  open,
  onClose,
  onSave,
  editDate,
  editTimeSlots,
  specificDayId,
}) => {
  const [container, setContainer] = useState<HTMLDivElement | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([
    { startTime: "08:00", endTime: "08:15", duration: 15 }
  ]);
  const [durations, setDurations] = useState<{ [key: number]: number }>({
    0: 15
  });

  useEffect(() => {
    if (typeof document !== "undefined") {
      // Ensure that the container exists in the DOM
      const portalContainer = document.getElementById("__next");
      if (!portalContainer) {
        const newContainer = document.createElement("div");
        newContainer.id = "portal-root";
        document.body.appendChild(newContainer);
        setContainer(newContainer);
      } else {
        setContainer(portalContainer as HTMLDivElement);
      }
    }
  }, []);

  useEffect(() => {
    if (typeof document !== "undefined") {
      const scrollbarElement = document.body;

      // Hide scrollbar when modal is open
      if (open && scrollbarElement.style.overflow !== "hidden") {
        scrollbarElement.setAttribute("style", "overflow: hidden");
      }

      // Close modal on 'Esc' key press
      const handleEscKeyPress = (event: KeyboardEvent) => {
        if (event.key === "Escape" && open) {
          onClose(); // Close modal
        }
      };

      document.addEventListener("keydown", handleEscKeyPress);

      // Cleanup the event listener and restore the scrollbar
      return () => {
        document.removeEventListener("keydown", handleEscKeyPress);
        if (open) {
          scrollbarElement.setAttribute("style", "overflow: auto");
        }
      };
    }
  }, [open, onClose]);

  // Reset state when modal opens or handle edit mode
  useEffect(() => {
    if (open) {
      // Always reset durations to default when modal opens
      setDurations({ 0: 15 });

      if (editDate && editTimeSlots && editTimeSlots.length > 0) {

        // Parse the date string - handle different formats
        let dateObj: Date;

        if (editDate.includes(", ")) {
          // Format: "DD, Month, YYYY"
          const [day, month, year] = editDate.split(", ");
          const monthIndex = months.indexOf(month);
          dateObj = new Date(parseInt(year), monthIndex, parseInt(day));
        } else if (editDate.includes("-")) {
          // Format: "DD-MM-YYYY" or similar
          const parts = editDate.split("-");
          if (parts.length === 3) {
            // Assuming day-month-year format
            dateObj = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
          } else {
            // Fallback to current date
            dateObj = new Date();
          }
        } else {
          // Try to parse as a date string
          dateObj = new Date(editDate);
          if (isNaN(dateObj.getTime())) {
            // If parsing fails, use current date
            dateObj = new Date();
          }
        }

        setCurrentDate(dateObj);
        setSelectedDate(dateObj);

        // Validate and add duration to each time slot
        const validTimeSlots = editTimeSlots.filter(slot => {
          // Check if the slot has valid startTime and endTime
          return slot && slot.startTime && slot.endTime;
        });

        // If there are no valid time slots, add a default one
        if (validTimeSlots.length === 0) {
          console.warn("No valid time slots found in edit data, using default");
          setTimeSlots([{ startTime: "08:00", endTime: "08:15", duration: 15 }]);
          setDurations({ 0: 15 });
          return;
        }

        // Add duration to each time slot if not already present
        setTimeSlots(validTimeSlots.map(slot => {
          if (slot.duration) return slot;

          try {
            // Calculate duration if not present
            const [startHour, startMinute] = slot.startTime.split(":").map(Number);
            const [endHour, endMinute] = slot.endTime.split(":").map(Number);

            const startTimeInMinutes = startHour * 60 + startMinute;
            let endTimeInMinutes = endHour * 60 + endMinute;

            // Handle case where end time is on the next day
            if (endTimeInMinutes < startTimeInMinutes) {
              endTimeInMinutes += 24 * 60; // Add 24 hours in minutes
            }

            return {
              ...slot,
              duration: endTimeInMinutes - startTimeInMinutes
            };
          } catch (error) {
            console.error("Error calculating duration for slot:", slot, error);
            // Return slot with default duration
            return {
              ...slot,
              duration: 15
            };
          }
        }));

        // Set durations based on time differences
        const newDurations: { [key: number]: number } = {};
        validTimeSlots.forEach((slot, index) => {
          try {
            if (!slot.startTime || !slot.endTime) {
              console.warn("Invalid time slot at index", index, "- missing startTime or endTime");
              newDurations[index] = 15; // Default duration
              return;
            }

            // If the slot already has a duration, use it
            if (slot.duration) {
              // Validate the duration to ensure it's one of the predefined values

              // Find the closest predefined duration
              const closestDuration = VALID_DURATIONS.reduce((prev, curr) => {
                return Math.abs(curr - slot.duration!) < Math.abs(prev - slot.duration!) ? curr : prev;
              });

              newDurations[index] = closestDuration;
              return;
            }

            // Calculate duration from start and end times
            const [startHour, startMinute] = slot.startTime.split(":").map(Number);
            const [endHour, endMinute] = slot.endTime.split(":").map(Number);

            const startTimeInMinutes = startHour * 60 + startMinute;
            let endTimeInMinutes = endHour * 60 + endMinute;

            // Handle cases where end time is on the next day
            if (endTimeInMinutes < startTimeInMinutes) {
              endTimeInMinutes += 24 * 60; // Add 24 hours in minutes
            }

            const calculatedDuration = endTimeInMinutes - startTimeInMinutes;

            // Validate the calculated duration to ensure it's one of the predefined values

            // Find the closest predefined duration
            const closestDuration = VALID_DURATIONS.reduce((prev, curr) => {
              return Math.abs(curr - calculatedDuration) < Math.abs(prev - calculatedDuration) ? curr : prev;
            });

            newDurations[index] = closestDuration;
          } catch (error) {
            console.error("Error calculating duration for slot at index", index, error);
            newDurations[index] = 15; // Default duration
          }
        });

        setDurations(newDurations);
      } else {
        // Default new mode
        setCurrentDate(new Date());
        setSelectedDate(null);
        setTimeSlots([{ startTime: "08:00", endTime: "08:15", duration: 15 }]);
        setDurations({ 0: 15 });
      }
    }
  }, [open, editDate, editTimeSlots]);

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  const handlePrevMonth = () => {
    setCurrentDate(prev => {
      const prevMonth = new Date(prev);
      prevMonth.setMonth(prev.getMonth() - 1);
      return prevMonth;
    });
  };

  const handleNextMonth = () => {
    setCurrentDate(prev => {
      const nextMonth = new Date(prev);
      nextMonth.setMonth(prev.getMonth() + 1);
      return nextMonth;
    });
  };

  const handleDateClick = (day: number) => {
    const newDate = new Date(currentDate);
    newDate.setDate(day);
    setSelectedDate(newDate);

    // When a new date is selected, ensure we have the default time slot with 15 minutes duration
    if (timeSlots.length === 0) {
      setTimeSlots([{ startTime: "08:00", endTime: "08:15", duration: 15 }]);
      setDurations({ 0: 15 });
    } else {
      // Ensure all durations are properly set to their default values
      const updatedDurations = { ...durations };
      timeSlots.forEach((_, index) => {
        if (!updatedDurations[index] || updatedDurations[index] === 240) {
          updatedDurations[index] = 15;
        }
      });
      setDurations(updatedDurations);
    }
  };

  const handleAddTimeSlot = () => {
    const newIndex = timeSlots.length;
    setTimeSlots(prev => [...prev, { startTime: "08:00", endTime: "08:15", duration: 15 }]);
    setDurations(prev => ({
      ...prev,
      [newIndex]: 15
    }));
  };

  const handleRemoveTimeSlot = (index: number) => {
    setTimeSlots(prev => prev.filter((_, i) => i !== index));

    // Update the durations object by removing the entry and reindexing
    const updatedDurations = { ...durations };
    delete updatedDurations[index];

    // Reindex the durations
    const newDurations: { [key: number]: number } = {};
    timeSlots.filter((_, i) => i !== index).forEach((_, i) => {
      if (i < index) {
        newDurations[i] = updatedDurations[i] || 15;
      } else {
        newDurations[i] = updatedDurations[i + 1] || 15;
      }
    });

    setDurations(newDurations);
  };

  const handleTimeChange = (index: number, field: 'startTime' | 'endTime', value: string) => {
    if (field === 'startTime') {
      // When start time changes, recalculate end time based on duration
      const duration = durations[index] || 15;

      // Parse the start time
      const [startHour, startMinute] = value.split(":").map(Number);
      const startTimeInMinutes = startHour * 60 + startMinute;

      // Calculate end time
      const totalMinutes = startTimeInMinutes + duration;
      const hour = Math.floor(totalMinutes / 60) % 24;
      const minute = totalMinutes % 60;
      const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
      const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
      const endTime = `${formattedHour}:${formattedMinute}`;

      setTimeSlots(prev => {
        const updated = [...prev];
        updated[index] = {
          startTime: value,
          endTime: endTime,
          duration: duration
        };
        return updated;
      });
    } else {
      // For end time, just update the value and recalculate duration
      setTimeSlots(prev => {
        const updated = [...prev];
        const slot = updated[index];

        // Check if slot exists and has a valid startTime
        if (!slot || !slot.startTime) {
          console.error("Invalid slot or missing startTime at index:", index);
          // Return the previous state unchanged
          return prev;
        }

        const [startHour, startMinute] = slot.startTime.split(":").map(Number);
        const [endHour, endMinute] = value.split(":").map(Number);

        const startTimeInMinutes = startHour * 60 + startMinute;
        let endTimeInMinutes = endHour * 60 + endMinute;

        // Handle case where end time is on the next day
        if (endTimeInMinutes < startTimeInMinutes) {
          endTimeInMinutes += 24 * 60; // Add 24 hours in minutes
        }

        const calculatedDuration = endTimeInMinutes - startTimeInMinutes;

        // Validate the calculated duration to ensure it's one of the predefined values

        // Find the closest predefined duration
        const closestDuration = VALID_DURATIONS.reduce((prev, curr) => {
          return Math.abs(curr - calculatedDuration) < Math.abs(prev - calculatedDuration) ? curr : prev;
        });


        // Recalculate the end time based on the validated duration
        const validatedEndTimeInMinutes = startTimeInMinutes + closestDuration;
        const validatedEndHour = Math.floor(validatedEndTimeInMinutes / 60) % 24;
        const validatedEndMinute = validatedEndTimeInMinutes % 60;
        const formattedEndHour = validatedEndHour < 10 ? `0${validatedEndHour}` : `${validatedEndHour}`;
        const formattedEndMinute = validatedEndMinute < 10 ? `0${validatedEndMinute}` : `${validatedEndMinute}`;
        const validatedEndTime = `${formattedEndHour}:${formattedEndMinute}`;

        updated[index] = {
          ...slot,
          endTime: validatedEndTime,
          duration: closestDuration
        };

        // Also update the durations state
        setDurations(prevDurations => ({
          ...prevDurations,
          [index]: closestDuration
        }));

        return updated;
      });
    }
  };

  const handleDurationChange = (index: number, duration: number) => {
    // Update the duration state
    setDurations(prev => ({
      ...prev,
      [index]: duration
    }));

    // Check if the time slot exists at the given index
    if (index < 0 || index >= timeSlots.length) {
      console.error("Invalid time slot index:", index);
      return;
    }

    // Get the time slot and check if it has a valid startTime
    const slot = timeSlots[index];
    if (!slot || !slot.startTime) {
      console.error("Invalid slot or missing startTime at index:", index);
      return;
    }

    // Update the end time based on the new duration
    const { startTime } = slot;

    // Parse the start time
    const [startHour, startMinute] = startTime.split(":").map(Number);
    const startTimeInMinutes = startHour * 60 + startMinute;

    // Calculate end time
    const totalMinutes = startTimeInMinutes + duration;
    const hour = Math.floor(totalMinutes / 60) % 24;
    const minute = totalMinutes % 60;
    const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
    const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
    const endTime = `${formattedHour}:${formattedMinute}`;

    // Update the time slot with duration
    setTimeSlots(prev => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        endTime: endTime,
        duration: duration
      };
      return updated;
    });
  };

  const handleApply = () => {
    if (selectedDate) {
      // Ensure all time slots have the correct duration before saving
      const updatedTimeSlots = timeSlots.map((slot, index) => {
        // Use the duration from our state, defaulting to 15 minutes
        const duration = durations[index] || 15;

        // Recalculate end time based on start time and duration to ensure consistency
        const [startHour, startMinute] = slot.startTime.split(":").map(Number);
        const startTimeInMinutes = startHour * 60 + startMinute;

        const totalMinutes = startTimeInMinutes + duration;
        const hour = Math.floor(totalMinutes / 60) % 24;
        const minute = totalMinutes % 60;
        const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;
        const formattedMinute = minute < 10 ? `0${minute}` : `${minute}`;
        const endTime = `${formattedHour}:${formattedMinute}`;

        return {
          startTime: slot.startTime,
          endTime: endTime,
          duration: duration
        };
      });

      const formattedDate = `${selectedDate.getDate()}, ${months[selectedDate.getMonth()]}, ${selectedDate.getFullYear()}`;
      onSave(formattedDate, updatedTimeSlots, specificDayId);
      onClose();
    }
  };

  const renderCalendar = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDay = getFirstDayOfMonth(year, month);

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="h-8 w-8"></div>);
    }

    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const isSelected = selectedDate &&
                         date.getDate() === selectedDate.getDate() &&
                         date.getMonth() === selectedDate.getMonth() &&
                         date.getFullYear() === selectedDate.getFullYear();

      days.push(
        <div
          key={day}
          className={`h-8 w-8 flex items-center justify-center rounded-full cursor-pointer
                     ${isSelected ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'}`}
          onClick={() => handleDateClick(day)}
        >
          {day}
        </div>
      );
    }

    return days;
  };

  if (!container || !open) {
    return null;
  }

  return ReactDOM.createPortal(
    <div
      className={`fixed inset-0 z-[99990] flex items-center justify-center bg-gray-800 bg-opacity-50 transition-opacity duration-300 ${
        open ? "opacity-100" : "opacity-0 pointer-events-none"
      }`}
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg shadow-lg transform transition-transform duration-300 overflow-hidden mx-4 my-6 sm:my-8 w-[50vh] max-h-[90vh] flex flex-col modal-container"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-xl font-bold"
          onClick={onClose}
          aria-label="Close modal"
        >
          <IoCloseCircleOutline className="w-5 h-5 md:w-7 md:h-7" />
        </button>

        <div className="flex flex-col h-full">
          <div className="p-6 overflow-y-auto flex-grow">
            <h2 className="text-xl font-semibold mb-4">
              Pick The Dates For Your Custom Time Slots
            </h2>

            {/* Calendar Header */}
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={handlePrevMonth}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <CaretLeft size={24} />
              </button>
              <h3 className="text-lg font-medium">
                {months[currentDate.getMonth()]} {currentDate.getFullYear()}
              </h3>
              <button
                onClick={handleNextMonth}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <CaretRight size={24} />
              </button>
            </div>

            {/* Calendar Grid */}
            <div className="mb-6">
              <div className="grid grid-cols-7 gap-1 mb-2">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                  <div key={day} className="text-center text-sm font-medium">
                    {day}
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-7 gap-1">
                {renderCalendar()}
              </div>
            </div>

            {/* Time Selection */}
            {selectedDate && (
              <>
                <div className="border-t pt-4 mb-4">
                  <h3 className="text-base font-medium mb-2">
                    At what times are you available or not available?
                  </h3>
                  <div className="max-h-[250px] overflow-y-auto pr-2 mb-2 relative z-10">
                    <div className="space-y-4 relative time-slots-container">
                      {timeSlots.map((slot, index) => (
                        <div key={index} className="flex items-center gap-2 mb-4 flex-wrap sm:flex-nowrap">
                          <div className="flex flex-col sm:flex-row sm:items-center w-full gap-2">
                            <div className="flex items-center w-full sm:w-auto">
                              <WorkingHoursTimePicker
                                value={slot.startTime}
                                onChange={(value: string) => handleTimeChange(index, 'startTime', value)}
                                isStartTime={true}
                                className="w-full sm:w-28"
                              />
                            </div>
                            <div className="flex items-center w-full sm:w-auto">
                              <DurationSelector
                                value={durations[index] || 15}
                                onChange={(duration: number) => handleDurationChange(index, duration)}
                                className="w-full sm:w-32"
                              />
                            </div>
                            <div className="flex items-center mx-1 hidden sm:block">
                              <span className="text-gray-500">-</span>
                            </div>
                            <div className="flex items-center w-full sm:w-auto">
                              <WorkingHoursTimePicker
                                value={slot.endTime}
                                onChange={() => {}}
                                isStartTime={false}
                                startTime={slot.startTime}
                                duration={durations[index] || 15}
                                className="w-full sm:w-28"
                              />
                            </div>
                            <div className="flex items-center justify-end mt-2 sm:mt-0">
                              {index === timeSlots.length - 1 ? (
                                <button
                                  onClick={handleAddTimeSlot}
                                  className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                                  title="Add another time slot"
                                >
                                  <Plus size={20} />
                                </button>
                              ) : (
                                <button
                                  onClick={() => handleRemoveTimeSlot(index)}
                                  className="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors"
                                  title="Remove this time slot"
                                >
                                  <Trash size={20} />
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleApply}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Apply
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>,
    container
  );
};

export default DatePickerModal;
