import axiosInstance from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import { AxiosError } from "axios";

// Define API response interfaces
export interface ApiResponse<T = unknown> {
  status: string;
  message: string;
  data: T;
  responseCode: number;
}

export interface ApiErrorResponse {
  status: string;
  message: string;
  errors: Array<{ code: string; message: string }>;
  responseCode: number;
}

// Define the time slot interface
export interface TimeSlot {
  startTime: string;
  endTime: string;
  duration?: number;
  apiDate?: string; // For storing the formatted date for API
  _id?: string; // MongoDB ID
}

// Calculate duration between two time strings in minutes
export const calculateDuration = (startTime: string, endTime: string): number => {
  const [startHour, startMinute] = startTime.split(":").map(Number);
  const [endHour, endMinute] = endTime.split(":").map(Number);

  const startTotalMinutes = startHour * 60 + startMinute;
  let endTotalMinutes = endHour * 60 + endMinute;

  // Handle case where end time is on the next day
  if (endTotalMinutes < startTotalMinutes) {
    endTotalMinutes += 24 * 60; // Add 24 hours in minutes
  }

  return endTotalMinutes - startTotalMinutes;
};

// Helper function to add duration to time slots and remove any _id fields
export const addDurationToTimeSlots = (timeSlots: TimeSlot[]): TimeSlot[] => {
  return timeSlots.map(slot => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { _id, ...slotWithoutId } = slot;
    return {
      ...slotWithoutId,
      duration: slotWithoutId.duration || calculateDuration(slotWithoutId.startTime, slotWithoutId.endTime)
    };
  });
};

// Transform specific day data for backend
export const transformSpecificDayForBackend = (specificDay: { date: string; timeSlots: TimeSlot[] }) => {
  // Use the apiDate from the first time slot if available, otherwise use the date from the specificDay
  let apiDate = specificDay.timeSlots[0]?.apiDate || specificDay.date;

  // Check if the date is in the format "DD, Month, YYYY" and convert it to "DD-MM-YYYY"
  if (apiDate.includes(',')) {
    const [day, month, year] = apiDate.split(', ');
    const monthIndex = ["January", "February", "March", "April", "May", "June",
                        "July", "August", "September", "October", "November", "December"]
                        .indexOf(month) + 1;

    const formattedMonth = monthIndex < 10 ? `0${monthIndex}` : `${monthIndex}`;
    const formattedDay = parseInt(day) < 10 ? `0${day}` : day;

    // Format as DD-MM-YYYY as required by the backend
    apiDate = `${formattedDay}-${formattedMonth}-${year}`;
  }
  // Check if the date is in MM-DD-YYYY format and convert to DD-MM-YYYY
  else if (apiDate.match(/^\d{2}-\d{2}-\d{4}$/)) {
    const [month, day, year] = apiDate.split('-');
    apiDate = `${day}-${month}-${year}`;
  }
  // Try to parse as a Date object if other formats fail
  else {
    try {
      const date = new Date(apiDate);
      if (!isNaN(date.getTime())) {
        const day = date.getDate();
        const month = date.getMonth() + 1;
        const year = date.getFullYear();

        const formattedDay = day < 10 ? `0${day}` : `${day}`;
        const formattedMonth = month < 10 ? `0${month}` : `${month}`;

        apiDate = `${formattedDay}-${formattedMonth}-${year}`;
      }
    } catch (e) {
      // Silently handle date parsing errors
      console.debug("Date parsing error:", e);
    }
  }

  // Final check to ensure date is in DD-MM-YYYY format
  if (apiDate.match(/^\d{2}-\d{2}-\d{4}$/)) {
    // Check if it's already in DD-MM-YYYY format or MM-DD-YYYY format
    const [first, second, year] = apiDate.split('-');

    // If first part is greater than 12, it's likely already in DD-MM-YYYY format
    if (parseInt(first) < 12) {
      apiDate = `${second}-${first}-${year}`;
    }
  }

  // Remove _id fields from slots to avoid validation errors
  return {
    date: apiDate,
    slots: addDurationToTimeSlots(specificDay.timeSlots).map(slot => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { apiDate, _id, ...slotWithoutExtraFields } = slot;
      return slotWithoutExtraFields;
    })
  };
};

// Define interface for specific working hours data
export interface SpecificWorkingHourDay {
  _id: string;
  date: string;
  slots: TimeSlot[];
}

// Fetch specific working hours
export const fetchSpecificWorkingHours = async (): Promise<SpecificWorkingHourDay[]> => {
  try {
    const specificDaysUrl = `${endpoints.specificWorkingHours}`;
    const specificDaysResponse = await axiosInstance.get<ApiResponse<SpecificWorkingHourDay[]>>(specificDaysUrl);

    // Check if we have valid data
    if (!specificDaysResponse.data || !specificDaysResponse.data.data) {
      return [];
    }

    // Filter out any specific days that have no slots
    const filteredData = specificDaysResponse.data.data.filter((day) => {
      return day.slots && day.slots.length > 0;
    });

    return filteredData || [];
  } catch (specificError) {
    // Log error for debugging purposes
    console.debug("Error fetching specific working hours:", specificError);
    // Return empty array if fetch fails
    return [];
  }
};

// Define interface for specific day data
export interface SpecificDayData {
  date: string;
  slots: TimeSlot[];
}

// Update a specific working hour by ID
export const updateSpecificWorkingHour = async (
  id: string,
  specificDayData: SpecificDayData
): Promise<ApiResponse<SpecificWorkingHourDay>> => {
  try {
    // Format the date properly for the API
    if (specificDayData.date) {
      // Check if the date is in "DD, Month, YYYY" format
      if (specificDayData.date.includes(", ")) {
        const [day, month, year] = specificDayData.date.split(", ");
        const monthIndex = ["January", "February", "March", "April", "May", "June",
                          "July", "August", "September", "October", "November", "December"]
                          .indexOf(month) + 1;

        const formattedMonth = monthIndex < 10 ? `0${monthIndex}` : `${monthIndex}`;
        const formattedDay = parseInt(day) < 10 ? `0${day}` : day;

        // Format as DD-MM-YYYY as required by the backend
        specificDayData.date = `${formattedDay}-${formattedMonth}-${year}`;
      }

      // Ensure date is in DD-MM-YYYY format
      if (specificDayData.date.match(/^\d{2}-\d{2}-\d{4}$/)) {
        // Check if it's already in DD-MM-YYYY format or MM-DD-YYYY format
        const [first, second, year] = specificDayData.date.split('-');

        // If first part is greater than 12, it's likely already in DD-MM-YYYY format
        if (parseInt(first) < 12) {
           specificDayData.date = `${second}-${first}-${year}`;
        }
      }
    }

    // Remove _id fields from slots
    if (specificDayData.slots && Array.isArray(specificDayData.slots)) {
      specificDayData.slots = specificDayData.slots.map((slot) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { _id, ...slotWithoutId } = slot;
        return slotWithoutId;
      });
    }

    const specificDayUrl = `${endpoints.specificWorkingHours}/${id}`;
    const response = await axiosInstance.patch<ApiResponse<SpecificWorkingHourDay>>(
      specificDayUrl,
      specificDayData
    );

    return response.data;
  } catch (error) {
    console.error("Error updating specific working hour:", error);
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as AxiosError;
      if (axiosError.response) {
        console.error("Error response data:", axiosError.response.data);
        console.error("Error response status:", axiosError.response.status);
      }
    }
    throw error;
  }
};

export const deleteSpecificWorkingHourSlot = async (
  id: string,
  date: string,
  slotIndex: number,
  currentSlots: TimeSlot[]
): Promise<ApiResponse<SpecificWorkingHourDay>> => {
  try {
    // Remove the slot at the specified index
    const updatedSlots = [...currentSlots];
    updatedSlots.splice(slotIndex, 1);

    // If no slots left, treat this as deleting the entire working hour
    if (updatedSlots.length === 0) {
      return await deleteSpecificWorkingHour(id, date);
    }

    // Normalize the date format to DD-MM-YYYY
    const formattedDate = formatDateToDDMMYYYY(date);

    // Prepare the data for PATCH (remove _id and optional keys)
    const sanitizedSlots = updatedSlots.map(slot => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { _id, apiDate, ...cleanSlot } = slot;
      if (!cleanSlot.duration) {
        cleanSlot.duration = calculateDuration(cleanSlot.startTime, cleanSlot.endTime);
      }
      return cleanSlot;
    });

    const specificDayData: SpecificDayData = {
      date: formattedDate,
      slots: sanitizedSlots
    };

    return await updateSpecificWorkingHour(id, specificDayData);
  } catch (error) {
    console.error("Error deleting slot:", error);
    throw error;
  }
};


// Save a single specific working hour
export const saveSpecificWorkingHour = async (
  specificDay: { date: string; timeSlots: TimeSlot[] }
): Promise<ApiResponse<SpecificWorkingHourDay>> => {
  try {
    const specificDayUrl = `${endpoints.specificWorkingHours}`;
    const specificDayData = transformSpecificDayForBackend(specificDay);

    // Skip if no slots are provided
    if (!specificDayData.slots || specificDayData.slots.length === 0) {
      throw new Error("No time slots provided");
    }

    // Final check to ensure date is in DD-MM-YYYY format
    if (specificDayData.date.match(/^\d{2}-\d{2}-\d{4}$/)) {
      // It's in the right format, but we need to check if day and month are swapped
      // Assume it's in MM-DD-YYYY format and swap to DD-MM-YYYY
      const [month, day, year] = specificDayData.date.split('-');
      specificDayData.date = `${day}-${month}-${year}`;
      // Date reformatted to ensure DD-MM-YYYY
    } else {
      // Date format is not DD-MM-YYYY before API call
      // Try to fix the format
      try {
        const date = new Date(specificDayData.date);
        if (!isNaN(date.getTime())) {
          const day = date.getDate();
          const month = date.getMonth() + 1;
          const year = date.getFullYear();

          const formattedDay = day < 10 ? `0${day}` : `${day}`;
          const formattedMonth = month < 10 ? `0${month}` : `${month}`;

          specificDayData.date = `${formattedDay}-${formattedMonth}-${year}`;
        }
      } catch (e) {
        console.error("Failed to reformat date:", e);
      }
    }

    // Check if any slot has an _id field
    const hasIdField = specificDayData.slots.some((slot) => '_id' in slot);
    if (hasIdField) {
      console.warn("Removing _id fields from slots to avoid validation errors");
      specificDayData.slots = specificDayData.slots.map((slot) => {
        const slotWithId = slot as TimeSlot & { _id?: string };
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { _id, ...slotWithoutId } = slotWithId;
        return slotWithoutId;
      });
    }

    // Direct fix for the payload - ensure date is in DD-MM-YYYY format
    if (specificDayData.date && specificDayData.date.match(/^\d{2}-\d{2}-\d{4}$/)) {
      // Check if it's already in DD-MM-YYYY format or MM-DD-YYYY format
      const [first, second, year] = specificDayData.date.split('-');

      // If first part is greater than 12, it's likely already in DD-MM-YYYY format
      if (parseInt(first) < 12) {
         specificDayData.date = `${second}-${first}-${year}`;
      }
    }

    const specificResponse = await axiosInstance.post<ApiResponse<SpecificWorkingHourDay>>(
      specificDayUrl,
      specificDayData
    );
    return specificResponse.data;
  } catch (error) {
    console.error("Error saving specific day:", error);
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as AxiosError;
      if (axiosError.response) {
        console.error("Error response data:", axiosError.response.data);
        console.error("Error response status:", axiosError.response.status);
      }
    }
    throw error;
  }
};

// Helper function to format date to DD-MM-YYYY format
const formatDateToDDMMYYYY = (date: string): string => {
  // Check if the date is in "DD, Month, YYYY" format
  if (date.includes(', ')) {
    const [day, month, year] = date.split(', ');
    const monthIndex = ["January", "February", "March", "April", "May", "June",
                      "July", "August", "September", "October", "November", "December"]
                      .indexOf(month) + 1;

    const formattedMonth = monthIndex < 10 ? `0${monthIndex}` : `${monthIndex}`;
    const formattedDay = parseInt(day) < 10 ? `0${day}` : day;

    // Format as DD-MM-YYYY as required by the backend
    return `${formattedDay}-${formattedMonth}-${year}`;
  }

  // Check if the date is in MM-DD-YYYY format and convert to DD-MM-YYYY
  if (date.match(/^\d{2}-\d{2}-\d{4}$/)) {
    const [first, second, year] = date.split('-');

    // If first part is greater than 12, it's likely already in DD-MM-YYYY format
    if (parseInt(first) > 12) {
      return date;
    } else {
      const formattedDate = `${second}-${first}-${year}`;
      return formattedDate;
    }
  }

  // If it's already in DD-MM-YYYY format, return as is
  return date;
};

export const deleteSpecificWorkingHour = async (
  id: string,
  date?: string
): Promise<ApiResponse<SpecificWorkingHourDay>> => {
  try {
    // Prepare the data for the PATCH request with empty slots array
    // Backend validation now allows min(0) slots
    const specificDayData: SpecificDayData = {
      date: date ? formatDateToDDMMYYYY(date) : "01-01-2025",
      slots: [] // Empty array since backend now accepts min(0)
    };

    const specificDayUrl = `${endpoints.specificWorkingHours}/${id}`;

    const response = await axiosInstance.patch<ApiResponse<SpecificWorkingHourDay>>(
      specificDayUrl,
      specificDayData
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting specific working hour:", error);
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as AxiosError;
      if (axiosError.response) {
        console.error("Error response data:", axiosError.response.data);
        console.error("Error response status:", axiosError.response.status);
      }
    }
    throw error;
  }
};

// Save multiple specific working hours (legacy function, kept for backward compatibility)
export const saveSpecificWorkingHours = async (
  specificDays: { date: string; timeSlots: TimeSlot[] }[]
): Promise<ApiResponse<SpecificWorkingHourDay>[]> => {
  try {
    const results: ApiResponse<SpecificWorkingHourDay>[] = [];

    // Process each specific day one by one
    for (const specificDay of specificDays) {
      try {
        const result = await saveSpecificWorkingHour(specificDay);
        results.push(result);
      } catch (specificError) {
        // Log error but continue with other specific days
        console.debug("Error saving specific day:", specificError);
        // Continue with other specific days even if one fails
      }
    }

    return results;
  } catch (error) {
    // This catch block is unlikely to be reached since errors are caught in the inner try-catch
    console.error("Unexpected error in saveSpecificWorkingHours:", error);
    throw error;
  }
};