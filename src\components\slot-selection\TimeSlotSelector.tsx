"use client";

import { WorkingHoursSlot } from "@/services/public-calendar.service";

interface TimeSlotSelectorProps {
  selectedDate: Date | null;
  availableTimeSlots: WorkingHoursSlot[];
  selectedTimeSlot: WorkingHoursSlot | null;
  onTimeSlotSelect: (slot: WorkingHoursSlot) => void;
  formatDate: (date: Date) => string;
  convertTo12Hour: (time24: string) => string;
  isLoadingSlots: boolean;
  isMobile?: boolean;
}

export default function TimeSlotSelector({
  selectedDate,
  availableTimeSlots,
  selectedTimeSlot,
  onTimeSlotSelect,
  formatDate,
  convertTo12Hour,
  isLoadingSlots,
  isMobile = false,
}: TimeSlotSelectorProps) {


  if (!selectedDate) {
    return (
      <div className="h-full flex items-center justify-center text-gray-500 py-20">
        <p className="text-lg">
          Please select a date to view available time slots
        </p>
      </div>
    );
  }

  // If we have a selected date but slots are still loading, show loading indicator
  if (isLoadingSlots) {
    return (
      <div className="h-full flex items-center justify-center py-16">
        <div className="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#718FFF] mb-2"></div>
        <p className="ml-3 text-gray-500 text-lg">
          Loading available slots...
        </p>
      </div>
    );
  }

  if (isMobile) {
    return (
      <div className="mb-6">
        <h3 className="text-[#251D5C] font-semibold mb-3 gilmer-bold">
          {formatDate(selectedDate)} ( {availableTimeSlots.length} Slot
          {availableTimeSlots.length !== 1 ? "s" : ""} Available )
        </h3>
        {availableTimeSlots.length > 0 ? (
          <div className="grid grid-cols-2 gap-2">
            {availableTimeSlots.map((slot, index) => (
              <div
                key={index}
                className={`border rounded-lg py-2 px-3 text-center text-sm cursor-pointer transition-all
                  ${
                    selectedTimeSlot &&
                    selectedTimeSlot._id === slot._id
                      ? "border-[#2C58BB] bg-[#2C58BB] font-medium text-white"
                      : "border-gray-200 hover:border-[#718FFF] hover:bg-[#718FFF]/5 text-[#251D5C] bg-gray-50"
                  }`}
                onClick={() => onTimeSlotSelect(slot)}
              >
                {`${convertTo12Hour(
                  slot.startTime
                )} - ${convertTo12Hour(slot.endTime)}`}
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-center py-4">
            No available slots for this date
          </p>
        )}
      </div>
    );
  }

  return (
    <div className="w-[60%] bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
      <h3 className="text-lg font-medium mb-5 gilmer-medium">
        {formatDate(selectedDate)}{" "}
        <span className="text-gray-500 text-base">
          ( {availableTimeSlots.length} Slot
          {availableTimeSlots.length !== 1 ? "s" : ""} Available )
        </span>
      </h3>
      {availableTimeSlots.length > 0 ? (
        <div className="grid grid-cols-3 gap-3">
          {availableTimeSlots.map((slot, index) => (
            <div
              key={index}
              className={`border rounded-lg py-2 px-2 text-center text-sm cursor-pointer transition-all
                ${
                  selectedTimeSlot &&
                  selectedTimeSlot._id === slot._id
                    ? "border-[#2C58BB] bg-[#2C58BB] font-medium text-white"
                    : "border-gray-200 hover:border-[#718FFF] hover:bg-[#718FFF]/5"
                }`}
              onClick={() => onTimeSlotSelect(slot)}
            >
              {`${convertTo12Hour(
                slot.startTime
              )} - ${convertTo12Hour(slot.endTime)}`}
            </div>
          ))}
        </div>
      ) : (
        <div className="h-full flex items-center justify-center text-gray-500 py-16">
          <p className="text-lg">
            No available slots for this date
          </p>
        </div>
      )}
    </div>
  );
}
