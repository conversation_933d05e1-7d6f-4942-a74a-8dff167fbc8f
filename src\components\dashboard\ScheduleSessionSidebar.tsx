import { X } from "@phosphor-icons/react";
import React, { useEffect, useState } from "react";
import Button from "../common/Button";
import Input from "../common/Input";
import SelectDropdown from "../common/SelectDropdown";
import DatePicker from "../common/DatePicker";
import TimePicker from "../common/TimePicker";
import { useFormik } from "formik";
import * as Yup from "yup";
import { createSchedule, getClientDetails } from "@/services/dashboard.service";
import moment from "moment";
// import toast from "react-hot-toast";
import { addMonths } from "@/helper/MomentHelper";

const genderOption = [
  "Cisgender Male",
  "Cisgender Female",
  "Transgender",
  "Non-Binary",
];

interface ClientDetails {
  email: string;
  phone?: string;
  age?: number;
  gender?: string;
  name?: string;
  defaultSessionAmount?: number;
}
const reminderOption = ["10-Minutes", "20-Minutes", "30-Minutes"];
// Validation Schema using Yup

const validationSchema = Yup.object({
  fromDate: Yup.date().required("Appointment date is required"),
  startTime: Yup.string()
    .required("Start time is required")
    .matches(
      /^([01]\d|2[0-3]):([0-5]\d)$/,
      "Start time must be in HH:mm format"
    )
    .test("is-valid", "Start time must be before end time.", function (value) {
      const { endTime } = this.parent; // Access sibling field value
      if (!value || !endTime) return true; // Skip if either field is empty
      return value < endTime; // Ensure startTime is earlier than endTime
    }),
  endTime: Yup.string()
    .required("End time is required")
    .matches(/^([01]\d|2[0-3]):([0-5]\d)$/, "End time must be in HH:mm format")
    .test("is-greater", "End time must be after start time.", function (value) {
      const { startTime } = this.parent; // Access sibling field value
      if (!startTime || !value) return true; // Skip if either field is empty
      return startTime < value; // Ensure endTime is later than startTime
    }),
  recurrence: Yup.string().required("Frequency is required"),
  name: Yup.string().required("Full name is required"),
  phone: Yup.string()
    .matches(/^[0-9]+$/, "Only digits are allowed")
    .min(10, "Mobile number must be at least 10 digits")
    .max(10, "Mobile number maximum 10 digits"),
  emails: Yup.array()
    .of(Yup.string().email("Invalid email address"))
    .min(1, "Email is required"),
  age: Yup.number().positive("Age must be a positive number"),
  amount: Yup.number()
    .positive("Payment must be a positive number")
    .required("Payment is required"),
  reminder: Yup.string().required("Reminder is required"),
  summary: Yup.string().required("Description is required"),
  // New enhanced fields validation
  maxMonths: Yup.number()
    .min(1, "Duration must be at least 1 month")
    .max(24, "Duration cannot exceed 24 months")
    .required("Duration is required"),
  autoExtend: Yup.boolean(),
  conflictResolution: Yup.string()
    .oneOf(['skip', 'warn', 'force'], "Invalid conflict resolution option"),
  extendThresholdDays: Yup.number()
    .min(1, "Threshold must be at least 1 day")
    .max(90, "Threshold cannot exceed 90 days"),
});

const MomentHelper = {
  getDateIST(date: string) {
    return moment(date).format("YYYY-MM-DD");
  },

  getTimeIST(time: string) {
    return moment(time).format("HH:mm");
  },

  convertToIST(date: string, time: string) {
    const [year, month, day] = date.split("-");
    const [hours, minutes] = time.split(":");
    const isoDate = new Date(
      Number(year),
      Number(month) - 1,
      Number(day),
      Number(hours),
      Number(minutes),
      0
    ).toISOString();
    return isoDate;
  },
};

const ScheduleSessionSidebar: React.FC<{
  isScheduleSessionModal: boolean;
  setIsScheduleSessionModal: (value: boolean) => void;
}> = ({ isScheduleSessionModal, setIsScheduleSessionModal }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [startDay, setStartDay] = useState<string>("Monday");

  const frequencyOption = [
    `Every Week On ${startDay}`,
    `Every Two Weeks ${startDay}`,
    "Does not repeat",
    // "Every Day",
  ];

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      fromDate: "",
      startTime: "",
      endTime: "",
      recurrence: "",
      toDate: "",
      name: "",
      phone: "",
      emails: [],
      age: "",
      gender: "",
      amount: "",
      reminder: "",
      summary: "",
      // New enhanced fields for calendar sync
      maxMonths: 12,
      autoExtend: false,
      conflictResolution: 'warn',
      extendThresholdDays: 30,
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      setIsSubmitting(true);

      // Destructure resetForm from Formik
      const { startTime, endTime, reminder, ...restValues } = values;
      console.log({ startTime, endTime, reminder });

      const startDate = MomentHelper.getDateIST(values?.fromDate);
      const fromDates = MomentHelper.convertToIST(startDate, values?.startTime);
      // Use the new maxMonths field instead of hardcoded 3 months
      const endDate = addMonths(startDate, values?.maxMonths || 12);

      const toDates = MomentHelper.convertToIST(endDate, values?.endTime);

      const formData = {
        ...restValues,
        clientCountry: "India",
        sessionDate: fromDates,
        description: "new session",
        location: "online",
        isBefore: false,
        fromDate: fromDates,
        toDate: toDates,
        // New enhanced fields for calendar sync
        maxMonths: values?.maxMonths || 12,
        autoExtend: values?.autoExtend || false,
        conflictResolution: values?.conflictResolution || 'warn',
        extendThresholdDays: values?.extendThresholdDays || 30,
      };

      // Add your form submit logic here
      const data = await createSchedule(formData);
      setIsSubmitting(false);

      // Reset the form, including startTime and endTime
      if (data.success) {
        setIsScheduleSessionModal(false);
        resetForm();

        // Refresh the page
        window.location.reload();
      }
    },
  });

  const [emailSuggestions, setEmailSuggestions] = useState<ClientDetails[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isNewClient, setIsNewClient] = useState(false);

  const handleEmailInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;
    setSearchQuery(input);
    formik.setFieldValue("emails", [input]);

    if (input.length >= 1) {
      try {
        const clientDetails = await getClientDetails(input);

        if (clientDetails?.data?.clients?.length > 0) {
          setEmailSuggestions(clientDetails.data.clients);
          setShowSuggestions(true);
          setIsNewClient(false);
        } else {
          setEmailSuggestions([]);
          setShowSuggestions(false);
          setIsNewClient(true);
        }

      } catch (error) {
        console.error("Error fetching email suggestions:", error);
        setEmailSuggestions([]);
        setShowSuggestions(false);
        setIsNewClient(true);
      }
    } else {
      setEmailSuggestions([]);
      setShowSuggestions(false);
      setIsNewClient(false);
    }
  };


  const handleSuggestionClick = (email: string, details: ClientDetails) => {
    setSearchQuery(email);
    formik.setFieldValue("emails", [email]);
    setShowSuggestions(false);

    if (details.phone) formik.setFieldValue("phone", details.phone);
    if (details.age !== undefined) formik.setFieldValue("age", details.age);
    if (details.gender) formik.setFieldValue("gender", details.gender);
    if (details.defaultSessionAmount !== undefined) formik.setFieldValue("amount", details.defaultSessionAmount);
    if (details.name) formik.setFieldValue("name", details.name);
  };

  const handleEmailBlur = async (email: string) => {
    if (formik.errors.emails) {
      return;
    }
    try {
      const clientDetails = await getClientDetails(email);

      if (clientDetails) {
        if (clientDetails.phone) {
          formik.setFieldValue("phone", clientDetails.phone);
        }
        if (clientDetails.age) {
          formik.setFieldValue("age", clientDetails.age);
        }
        if (clientDetails.gender) {
          formik.setFieldValue("gender", clientDetails.gender);
        }
        if (clientDetails.defaultSessionAmount) {
          await formik.setFieldValue(
            "amount",
            Number(clientDetails.defaultSessionAmount)
          );
          setTimeout(async () => {
            await formik.validateField("amount");
          }, 0);
        }
        if (clientDetails.name) {
          await formik.setFieldValue("name", clientDetails.name);
          setTimeout(async () => {
            await formik.validateField("name");
          }, 0);
        }
      }
    } catch (error) {
      console.error("Error fetching client details:", error);
    }
  };

  useEffect(() => {
    const handleBodyScroll = (shouldLock: boolean) => {
      if (shouldLock) {
        document.body.style.overflow = "hidden";
      } else {
        document.body.style.overflow = "";
      }
    };

    handleBodyScroll(isScheduleSessionModal);

    return () => handleBodyScroll(false);
  }, [isScheduleSessionModal]);

  return (
    <div
      className={`fixed w-full h-full bg-black/20 top-0 left-0 z-[999] ${
        isScheduleSessionModal ? "visible" : "invisible"
      }`}
      onClick={() => {
        setIsScheduleSessionModal(false);
        formik.resetForm();
      }}
    >
      <div
        className={`max-w-[416px] w-full  bg-white absolute top-0 right-0 h-full transition-all duration-300 ${
          isScheduleSessionModal ? "translate-x-0" : "translate-x-full"
        }`}
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside the sidebar
      >
        <div className="relative flex flex-col h-[100svh] sm:h-screen overflow-y-auto">
          {/* side bar header */}
          <div className="px-5 py-3.5 shadow-[0px_4px_12px_0px_#0000000F] flex justify-between items-center sticky top-0">
            <h3 className="text-lg font-medium text-[#242424]">
              Schedule Session
            </h3>
            <button
              onClick={() => {
                setIsScheduleSessionModal(false);
                formik.resetForm();
              }}
            >
              <X size={20} />
            </button>
          </div>
          {/* Loader overlay */}
          {isSubmitting && (
            <div className="absolute inset-0 flex justify-center items-center bg-black/50 z-[1000]">
              <div className="spinner-border animate-spin h-12 w-12 border-4 border-t-transparent border-white rounded-full"></div>
            </div>
          )}

          {/* content */}
          <form
            onSubmit={formik.handleSubmit}
            className="p-5 flex-1 overflow-auto"
          >
            <div>
              <h4 className="text-sm/5 font-medium text-[#5E585A]">
                Clients details
              </h4>
              <div className="grid grid-cols-2 gap-5 mt-[15px]">
                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Full Name <span className="text-red-600">*</span>
                  </label>
                  <Input
                    name="name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter Full Name"
                  />
                  {formik.touched.name && formik.errors.name ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.name}
                    </div>
                  ) : null}
                </div>

                <div className="col-span-2 relative">
                  <label className="text-sm/5 text-primary font-medium">
                    Email <span className="text-red-600">*</span>
                  </label>
                  <Input
                    name="emails"
                    value={searchQuery}
                    onChange={handleEmailInputChange}
                    onBlur={(e) => handleEmailBlur(e.target.value)}
                    placeholder="Enter Email"
                    type="email"
                  />
                  {showSuggestions && emailSuggestions.length > 0 && (
                  <ul className="absolute bg-white border border-gray-300 w-full z-50 shadow-lg max-h-40 overflow-y-auto">
                    {emailSuggestions.map((suggestion, index) => {
                      return (
                        <li
                          key={index}
                          onMouseDown={(e) => e.preventDefault()} // Prevent dropdown from closing before selection
                          onClick={() => handleSuggestionClick(suggestion.email, suggestion)}
                          className="p-2 hover:bg-gray-100 cursor-pointer"
                        >
                          {suggestion.email}
                        </li>
                      );
                    })}
                  </ul>
                )}
                {isNewClient && (
                  <div className="mt-2 inline-block bg-[#C58843] text-white text-xs font-medium px-3 py-1 rounded-full shake-animation">
                    New Client Detected
                  </div>
                )}
                  {formik.touched.emails && formik.errors.emails ? (
                    <div className="text-red-600 text-sm">{formik.errors.emails}</div>
                  ) : null}
                </div>

                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Mobile Number
                  </label>
                  <Input
                    name="phone"
                    value={formik.values.phone}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter Number"
                    type="tel"
                  />
                  {formik.touched.phone && formik.errors.phone ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.phone}
                    </div>
                  ) : null}
                </div>

                <div>
                  <label className="text-sm/5 text-primary font-medium">
                    Age {/* <span className="text-red-600">*</span> */}
                  </label>
                  <Input
                    name="age"
                    value={formik.values.age}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter Age"
                    type="text"
                  />
                  {formik.touched.age && formik.errors.age ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.age}
                    </div>
                  ) : null}
                </div>
                <div>
                  <label className="text-sm/5 text-primary font-medium">
                    Gender {/* <span className="text-red-600">*</span> */}
                  </label>
                  <SelectDropdown
                    options={genderOption}
                    value={formik.values.gender}
                    onChange={(val) => formik.setFieldValue("gender", val)}
                    placeholder="Select ..."
                  />
                  {formik.touched.gender && formik.errors.gender ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.gender}
                    </div>
                  ) : null}
                </div>
                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Amount <span className="text-red-600">*</span>
                  </label>
                  <Input
                    name="amount"
                    value={formik.values.amount}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter amount"
                    type="number"
                  />
                  {formik.touched.amount && formik.errors.amount ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.amount}
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
            <hr className="border-divider my-5" />
            <div className="grid grid-cols-2 gap-5">
              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Appointment Date <span className="text-red-600">*</span>
                  <DatePicker
                    placeholder="DD/MM/YYYY"
                    value={formik.values.fromDate}
                    minDateToday={true}
                    onChange={(val) => {
                      const dateValue = new Date(val); // Create a Date object from the string
                      // Get the day name (e.g., "Monday", "Tuesday")
                      const dayName = dateValue.toLocaleDateString("en-US", {
                        weekday: "long", // Full day name (Monday, Tuesday)
                      });
                      setStartDay(dayName);
                      if (formik.values.recurrence) {
                        formik.setFieldValue("recurrence", "");
                      }
                      formik.setFieldValue("fromDate", val);
                    }}
                  />
                </label>

                {formik.touched.fromDate && formik.errors.fromDate ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.fromDate}
                  </div>
                ) : null}
              </div>

              <div>
                <label className="text-sm/5 text-primary font-medium">
                  Start Time <span className="text-red-600">*</span>
                </label>
                <TimePicker
                  value={formik.values.startTime || ""}
                  fromDate={
                    formik.values.fromDate
                      ? new Date(formik.values.fromDate)
                      : null
                  } // Convert to Date if exists
                  onChange={(val) => {
                    formik.setFieldValue("startTime", val); // Set valid start time
                  }}
                />
                {formik.touched.startTime && formik.errors.startTime ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.startTime}
                  </div>
                ) : null}
              </div>

              <div>
                <label className="text-sm/5 text-primary font-medium">
                  End Time <span className="text-red-600">*</span>
                </label>
                <TimePicker
                  fromDate={
                    formik.values.fromDate
                      ? new Date(formik.values.fromDate)
                      : null
                  } // Convert to Date if exists
                  value={formik.values.endTime || ""}
                  onChange={(val) => {
                    formik.setFieldValue("endTime", val);
                  }}
                />
                {formik.touched.endTime && formik.errors.endTime ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.endTime}
                  </div>
                ) : null}
              </div>
              {/* <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  End Date
                </label>
                <DatePicker
                  minDateToday={true}
                  placeholder="DD/MM/YYYY"
                  value={formik.values.toDate}
                  minDate={formik.values.fromDate}
                  onChange={(val) => formik.setFieldValue("toDate", val)}
                />
                {formik.touched.toDate && formik.errors.toDate ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.toDate}
                  </div>
                ) : null}
              </div> */}
              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Frequency <span className="text-red-600">*</span>
                </label>
                <SelectDropdown
                  options={frequencyOption}
                  value={formik.values.recurrence}
                  onChange={(val) => formik.setFieldValue("recurrence", val)}
                  placeholder="Select ..."
                />
                {formik.touched.recurrence && formik.errors.recurrence ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.recurrence}
                  </div>
                ) : null}
              </div>
              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Description <span className="text-red-600">*</span>
                </label>
                <textarea
                  name="summary"
                  className="block w-full mt-2 p-3 border border-green-600/20 rounded-lg outline-none text-sm text-primary bg-transparent focus:border-green-600"
                  placeholder="Enter Description"
                  value={formik.values.summary}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                ></textarea>
                {formik.touched.summary && formik.errors.summary ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.summary}
                  </div>
                ) : null}
              </div>
              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Reminder <span className="text-red-600">*</span>
                </label>
                <SelectDropdown
                  options={reminderOption}
                  value={formik.values.reminder}
                  onChange={(val) => formik.setFieldValue("reminder", val)}
                  placeholder="Select ..."
                />
                {formik.touched.reminder && formik.errors.reminder ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.reminder}
                  </div>
                ) : null}
              </div>
            </div>

            {/* Enhanced Calendar Sync Settings */}
            <hr className="border-divider my-5" />
            <div>
              <h4 className="text-sm/5 font-medium text-[#5E585A] mb-4">
                Enhanced Calendar Sync Settings
              </h4>
              <div className="grid grid-cols-2 gap-5">
                {/* Duration Selector */}
                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Session Duration (Months) <span className="text-red-600">*</span>
                  </label>
                  <div className="mt-2">
                    <input
                      type="range"
                      min="1"
                      max="24"
                      value={formik.values.maxMonths}
                      onChange={(e) => formik.setFieldValue("maxMonths", parseInt(e.target.value))}
                      className="duration-slider w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>1 month</span>
                      <span>24 months</span>
                    </div>
                  </div>
                  <div className="session-preview mt-2 text-center">
                    <span className="text-sm font-medium text-blue-700">
                      Duration: {formik.values.maxMonths} month{formik.values.maxMonths > 1 ? 's' : ''}
                    </span>
                    {formik.values.recurrence && formik.values.recurrence !== "Does not repeat" && (
                      <span className="block text-xs text-blue-600 mt-1">
                        Will create approximately {
                          formik.values.recurrence.includes("Every Two Weeks")
                            ? Math.floor(formik.values.maxMonths * 2.17)
                            : formik.values.maxMonths * 4.33
                        } sessions
                      </span>
                    )}
                  </div>
                  {formik.touched.maxMonths && formik.errors.maxMonths ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.maxMonths}
                    </div>
                  ) : null}
                </div>

                {/* Auto-Extend Toggle */}
                <div className="col-span-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm/5 text-primary font-medium">
                      Auto-Extend Sessions
                    </label>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formik.values.autoExtend}
                        onChange={(e) => formik.setFieldValue("autoExtend", e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                    </label>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Automatically extend sessions when nearing end date
                  </p>
                </div>

                {/* Extend Threshold (only show if auto-extend is enabled) */}
                {formik.values.autoExtend && (
                  <div className="col-span-2">
                    <label className="text-sm/5 text-primary font-medium">
                      Auto-Extend Threshold (Days)
                    </label>
                    <Input
                      name="extendThresholdDays"
                      value={formik.values.extendThresholdDays.toString()}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="30"
                      type="number"
                      min="1"
                      max="90"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Extend sessions when {formik.values.extendThresholdDays} days remain
                    </p>
                    {formik.touched.extendThresholdDays && formik.errors.extendThresholdDays ? (
                      <div className="text-red-600 text-sm">
                        {formik.errors.extendThresholdDays}
                      </div>
                    ) : null}
                  </div>
                )}

                {/* Conflict Resolution */}
                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Conflict Resolution
                  </label>
                  <div className="mt-2 space-y-2">
                    {[
                      { value: 'warn', label: 'Warn me about conflicts (Recommended)', desc: 'Show conflicts for manual review' },
                      { value: 'skip', label: 'Skip conflicting sessions', desc: 'Automatically skip sessions that conflict' },
                      { value: 'force', label: 'Force create all sessions', desc: 'Create sessions even if conflicts exist' }
                    ].map((option) => (
                      <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                        <input
                          type="radio"
                          name="conflictResolution"
                          value={option.value}
                          checked={formik.values.conflictResolution === option.value}
                          onChange={formik.handleChange}
                          className="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                        />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{option.label}</div>
                          <div className="text-xs text-gray-500">{option.desc}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                  {formik.touched.conflictResolution && formik.errors.conflictResolution ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.conflictResolution}
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          </form>

          {/* side bar footer */}
          <div className="bg-white shadow-[0px_4px_43.4px_0px_#0000001A] px-5 py-2.5 grid grid-cols-2 gap-5 sticky bottom-0 z-10">
            <Button
              onClick={() => {
                formik.resetForm();
                setIsScheduleSessionModal(false);
              }}
              variant="outlinedGreen"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              onClick={formik.handleSubmit}
              variant="filledGreen"
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduleSessionSidebar;
