@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #f5f5f7;
  --foreground: #171717;
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  position: relative;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* scroll bar style */
::-webkit-scrollbar {
  width: 0px;
  height: 2px;
  background-color: #f5f5f5;
}

::-webkit-scrollbar-thumb {
  background-color: #d0d1d2;
  border-radius: 20px;
}

/* dash board container  */
.dashboard_container {
  max-width: 1392px;
  margin: 0 auto;
}

@media screen and (max-width: 1392px) {
  .dashboard_container {
    max-width: 1100px;
  }
}

/* moveUpDown animation  */

.animate-move {
  animation: moveUpDown 3s ease-in-out infinite;
}

@keyframes moveUpDown {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* marquee animation start */

.client_marquee_anim {
  animation: marqueeH 40s linear infinite;
}

@keyframes marqueeH {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}

.client_marquee_anim_wrap:hover .client_marquee_anim {
  animation-play-state: paused;
}

.client_marquee_anim_wrap:hover .review_image {
  filter: grayscale(100%);
}

.review_card:hover .review_image {
  filter: none;
}

/* marquee animation end */

/* globals.css or a custom CSS file */
/* #nprogress .bar {
  @apply bg-yellow-600 h-3
}

#nprogress .peg {
  box-shadow: 0 0 10px #C58843, 0 0 5px #C58843;
} */

#nprogress {
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  z-index: 9999;
  position: fixed;
  pointer-events: none;
}
#nprogress .bar {
  height: 100%;
  background-color: #c58843;
  box-shadow: 0 0 2.5px #c58843;
}
#nprogress .peg {
  right: 0;
  opacity: 1;
  width: 100px;
  height: 100%;
  display: block;
  position: absolute;
  transform: rotate(3deg) translate(0px, -4px);
  box-shadow: 0 0 10px #c58843, 0 0 5px #c58843;
}

@keyframes shake {
  0% { transform: translateX(0); }
  20% { transform: translateX(-3px); }
  40% { transform: translateX(3px); }
  60% { transform: translateX(-3px); }
  80% { transform: translateX(3px); }
  100% { transform: translateX(0); }
}

.shake-animation {
  animation: shake 0.4s ease-in-out 2;
}


@keyframes leftRight {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(10px);
  }
}

.animate-left-right {
  animation: leftRight 1.2s ease-in-out infinite;
}

.__toast-container {
  z-index: 10000 !important;
}

/* For Chrome, Safari, Edge, Opera */
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* For Firefox */
input[type=number] {
  -moz-appearance: textfield;
}
