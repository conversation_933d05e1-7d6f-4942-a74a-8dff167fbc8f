import Image from "next/image";
import Link from "next/link";
import { FC } from "react";
import Logo from "../../../public/assets/images/newHome/footer-logo.png";
import { FaInstagram, FaLinkedinIn, FaYoutube } from "react-icons/fa";


const Footer: FC = () => {
  return (
    <footer
      className="py-10 px-4 text-black"
      style={{
        backgroundImage: "url('/assets/images/newHome/bg-Home.png')",
        backgroundSize: "contain",
      }}
    >
      <div className="max-w-9xl w-full text-sm">
        {/* Mobile Layout */}
        <div className="block max-[820px]:block min-[821px]:hidden space-y-6">
          {/* Logo */}
          <div className="flex items-start">
            <Image
              src={Logo}
              alt="Thought Pudding Logo"
              width={180}
              height={70}
              className="w-auto h-auto"
            />
          </div>

          {/* About Us + Legals side-by-side */}
          <div className="flex pt-6 justify-between gap-6">
            {/* About Us */}
            <div>
              <h3 className="font-semibold text-base mb-3">About Us</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="https://www.blog.thoughtpudding.com">Learn About Therapy</Link>
                </li>
                <li>
                  <Link href="#">Find Therapist</Link>
                </li>
                <li>
                  <Link href="#subscription">Pricing</Link>
                </li>
                <li>
                  <Link href="https://calendly.com/thoughtpuddingdemo/making-private-practice-easy-demo-call">Sign Up as Therapist</Link>
                </li>
              </ul>
            </div>
            {/* Legals */}
            <div>
              <h3 className="font-semibold text-base mb-3">Legals</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/privacy-policy">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/terms-condictions">
                    Terms & Conditions
                  </Link>
                </li>
                <li>
                  <Link href="#">Cookies Policy</Link>
                </li>
                <li>
                  <Link href="#">Disclaimer</Link>
                </li>
              </ul>
            </div>
          </div>

          {/* Social Links */}
          <div className="pt-6">
            <h3 className="font-semibold text-base mb-3">Follow Us</h3>
            <div className="flex gap-4 text-xl">
              <Link
                href="https://www.instagram.com/thoughtpuddingfortherapists/"
                target="_blank"
              >
                <FaInstagram />
              </Link>
              <Link
                href="https://www.linkedin.com/company/thought-pudding-for-therapists"
                target="_blank"
              >
                <FaLinkedinIn />
              </Link>
              <Link href="#" target="_blank">
                <FaYoutube />
              </Link>
            </div>
          </div>

          {/* Mobile Layout - Address Section */}
          <div className="pt-4">
            <div className="space-y-2 text-sm">
              <p className="font-medium">Registered Office:</p>
              <p>B-30, first floor, Lajpat Nagar 3,</p>
              <p>New Delhi -110024</p>
              <p className="font-medium pt-2">Connect:</p>
              <div >
                <Link 
                  href="mailto:<EMAIL>"
                  className="hover:text-primary transition-colors"
                >
                  <EMAIL>
                </Link>
              </div>
            </div>
          </div>

          {/* Copyright */}
          <p className="text-xs mt-4 pt-6">
            &copy; 2025 Thought Pudding. All Rights Reserved
          </p>
        </div>

        {/* Desktop Layout */}
        <div className="hidden min-[821px]:grid grid-cols-[auto_1fr_1fr_1fr] [@media(min-width:1281px)]:gap-x-[120px] gap-x-6 text-left w-full">
          {/* Logo */}
          <div className="flex flex-col items-start justify-between">
            <Image
              src={Logo}
              alt="Thought Pudding Logo"
              width={370}
              height={140}
              className="w-auto h-auto"
            />
            <p className="text-[12px] mt-4 max-w-[300px]">
              &copy; 2025 Thought Pudding. All Rights Reserved
            </p>
          </div>

          {/* About Us */}
          <div>
            <h3 className="font-semibold text-base md:text-xl mb-4">
              About Us
            </h3>
            <ul className="space-y-3 text-sm">
              <li>
                <Link href="https://www.blog.thoughtpudding.com">Learn About Therapy</Link>
              </li>
              <li>
                <Link href="#">Find Therapist</Link>
              </li>
              <li>
                <Link href="#subscription">Pricing</Link>
              </li>
              <li>
                <Link href="https://calendly.com/thoughtpuddingdemo/making-private-practice-easy-demo-call">Sign Up as Therapist</Link>
              </li>
            </ul>
          </div>

          {/* Legals */}
          <div>
            <h3 className="font-semibold text-base md:text-xl mb-4">Legals</h3>
            <ul className="space-y-3 text-sm">
              <li>
                <Link href="/privacy-policy">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms-condictions">
                  Terms & Conditions
                </Link>
              </li>
              <li>
                <Link href="#">Cookies Policy</Link>
              </li>
              <li>
                <Link href="#">Disclaimer</Link>
              </li>
            </ul>
          </div>

          {/* Social Links */}
          <div>
            <h3 className="font-semibold text-base md:text-xl mb-4">
              Follow Us
            </h3>
            <div className="flex gap-4 text-xl">
              <Link
                href="https://www.instagram.com/thoughtpuddingfortherapists/"
                target="_blank"
              >
                <FaInstagram />
              </Link>
              <Link
                href="https://www.linkedin.com/company/thought-pudding-for-therapists"
                target="_blank"
              >
                <FaLinkedinIn />
              </Link>
              <Link href="#">
                <FaYoutube />
              </Link>
            </div>

            {/* Desktop Address Section */}
            <div className="pt-6">
              <div className="space-y-1 text-sm">
                <p className="font-medium">Registered Office:</p>
                <p>B-30, first floor, Lajpat Nagar 3,</p>
                <p>New Delhi -110024</p>
                <p className="font-medium pt-2">Connect:</p>
                <div>
                  <Link 
                    href="mailto:<EMAIL>"
                    className="hover:text-primary transition-colors"
                  >
                    <EMAIL>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
