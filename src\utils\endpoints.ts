const endpoints = {
  login: "/therapist/signup",
  createTherapist: "/therapist/create",
  updateTherapist: "/therapist/update",
  timeZones: "/therapist/timezones",
  therapistNotification: "/notification/therapist/all",
  publicCalendar: "/therapist/onboarding",
  getTherapistById: "/therapist/get",
  workingHours: "/therapist/working-hours",
  specificWorkingHours: "/therapist/specific-working-hours",
  otp: {
    send: "/client/otp/send",
    verify: "/client/otp/verify",
  },
  booking: {
    schedule: "/therapist/schedule-booking",
  },

  sessions: {
    schedules: `/therapist/schedules`,
    reschedule: `/therapist/reschedule`,
    entireSlote: `/therapist/google/events/update`,
    rescheduleCalendarSeries: `/therapist/schedule/reschedule-calendar-series`,
    rescheduleSession: `/therapist/schedule/reschedule-session`,
    rescheduleSeries: `/therapist/schedule/reschedule-series`,
    updateRescheduledSession: `/therapist/calendar/update-rescheduled-session`,
    deleteCalendarSessions: `/therapist/calendar/delete-sessions`,
    cancelSchedule: `/therapist/schedule/cancel`,
    recreatePaymentLink: `/therapist/payment/recreate`,
    scheduleCount: `/therapist/schedules/count`,
    scheduleReminder: `/therapist/schedule/reminder`,
  },
  clients: {
    clients: `/therapist/clients`,
    clientCount: `/therapist/client/count`,
    getClientById: `/client/getClientById`,
    putClientById: `/therapist/client`,
  },
  payments: {
    payments: `/therapist/payments`,
    therapistPaymentStats: `/therapist/payment/stats`,
    sendReminder: `/therapist/reminder/send`,
    searchPayment: `/therapist/payment/search`,
  },
  paymentTracker: {
    getAllPayment: "/therapist/paytracker/get/all",
    deletePayment: "/therapist/paytracker/delete",
    getPaymentsByTherapist: "/therapist/paytracker/get",
    changePaytrackerStatus: "/therapist/paytracker/change/status",
    getStats: "/therapist/paytracker/get/stats",
    getClientsOfTherapist: "therapist/paytracker/get/clients",
    getPayTrackerEmuns: "therapist/paytracker/enums",
    chargeSession: "/therapist/paytracker/charge/session",
    sendReminder: "/therapist/paytracker/send/reminder",
    updateAmount: "/therapist/paytracker/update/amount",
  },
  dashboard: {
    createSchedule: "/therapist/schedules/create",
    getDashboardStats: "/therapist/dashboard/stats",
    getMostRecentClient: "/therapist/regular",
    getCancellation: "/therapist/cancelled/session",
    getFreeSlot: "/therapist/free-slot",
    getClientDetails: "/therapist/clients/email",
    checkSyncStatus: "/therapist/calendar/checkSynedStatus",
  },
  setting: {
    therapistSettingData: "/therapist/data",
    uploadProfile: "/therapist/profile/upload",
    therapistVpaUpdate: "/therapist/vpa/update",
    paymentMenuUpdate: "/therapist/menu/update",
    calendarResync: "/therapist/calendar/resync",
    enhancedCalendarResync: "/api/v1/therapist/calendar/resync", // Enhanced version with new/rescheduled separation
    calendarEventClients: "/therapist/getSpecificCalendarEventClients",
    createMultipleClients: "/client/createMultipleClients",
    calendarEvents: "/therapist/getCalendarEvents",
    eventCalender: "therapist/getScheduleList",
    eventSync: "/therapist/getSpecificCalendarEvents",
    updateSyncStatus: "/therapist/calendar/update/syncDate",
    // New webhook endpoints for real-time sync
    webhook: {
      setup: "/webhook/setup",
      status: "/webhook/status",
      stop: "/webhook/stop",
      renew: "/webhook/renew",
      manualSync: "/webhook/sync/manual",
      statistics: "/webhook/sync/statistics",
    },
    subscription: {
      paymentHistory: "/therapist/subscription/active/get",
      plan: "/therapist/subscription/get",
      createOrder: "/therapist/subscription/transaction",
      verifyPayment: "/therapist/subscription/verify",
      validTherapistSubscription: "/therapist/subscription/valid",
      activateTrial: "/therapist/subscription/activate-trial"
    },
  },
  verifyTherapist: "therapist/verify-therapist",
  getTherapistVerificationData: "therapist/vetificationStatus",
  addPractice: "therapist/add-pratice",
};

export default endpoints;