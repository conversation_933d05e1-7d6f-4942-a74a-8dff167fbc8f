"use client";
import React, { useState, useEffect } from "react";
import DashboardLayout from "@/layout/dashboard/DashboardLayout";
import { tabs, tabRedirects } from "../tabs/tabsData";
import { useRouter } from "next/navigation";
import { isTabCompleted, markTabAsCompleted } from "@/utils/completedTabs";
import { fetchBookingMessage, saveBookingMessage } from "@/services/booking-message.service";
import Loader from "@/components/common/Loader";
import AnimatedButton from "@/components/common/AnimatedButton";
import TransitionLoader from "@/components/common/TransitionLoader";

const BookingMessagePage = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("Successful Booking Message");
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [redirectCountdown, setRedirectCountdown] = useState(0);
  const [error, setError] = useState("");

  // Fetch booking message when component mounts
  useEffect(() => {
    const getBookingMessage = async () => {
      try {
        setIsDataLoading(true);
        const data = await fetchBookingMessage();
        setMessage(data || "Thanks for booking your session with me. To complete your booking, kindly pay the session fees 48 hours before your session at UPI ID: abc@paytm.....\n\n\nWrite your own");
      } catch (error) {
        console.error("Error fetching booking message:", error);
        // Set default message if fetch fails
        setMessage("Thanks for booking your session with me. To complete your booking, kindly pay the session fees 48 hours before your session at UPI ID: abc@paytm.....\n\n\nWrite your own");
      } finally {
        setIsDataLoading(false);
      }
    };

    getBookingMessage();
  }, []);

  // Handle message change
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);

    // Clear error when user starts typing
    if (value.trim() && error) {
      setError("");
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!message.trim()) {
      setError("Message cannot be empty");
      return;
    }

    setIsLoading(true);
    try {
      // Save booking message using the service
      await saveBookingMessage(message);

      // Set success state
      setSaveSuccess(true);

      // Mark this tab as completed
      markTabAsCompleted(activeTab);

      // Start countdown for redirection (3 seconds)
      setRedirectCountdown(3);

      // Set up countdown timer
      const countdownInterval = setInterval(() => {
        setRedirectCountdown(prev => {
          if (prev <= 1) {
            clearInterval(countdownInterval);
            setIsLoading(false);

            // Navigate to dashboard when countdown reaches 0
            router.push("/dashboard");
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      console.error("Error saving booking message:", error);
      setIsLoading(false);
    }
  };

  // Calculate progress percentage for the transition loader
  const calculateProgress = () => {
    if (!saveSuccess) return 0;
    return ((3 - redirectCountdown) / 3) * 100;
  };

  return (
    <DashboardLayout>
      {/* Transition loader - shown during saving and redirection */}
      <TransitionLoader
        isVisible={saveSuccess}
        message="Booking message saved successfully!"
        redirectMessage="Redirecting to Dashboard..."
        progress={calculateProgress()}
      />
      <div className="p-0">
        {/* White background container with rounded corners */}
        <div className="bg-white rounded-xl shadow-sm">
          {/* Tabs */}
          <div className="mb-8">
            <div className="flex overflow-x-auto no-scrollbar mt-4">
              {tabs.map((tab, index) => {
                // Determine if this tab should be disabled
                const currentTabIndex = tabs.indexOf(activeTab);
                // Check if the tab is completed or is the current tab
                const isCompleted = isTabCompleted(tab);
                const isDisabled = index > currentTabIndex && !isCompleted;

                return (
                  <button
                    key={tab}
                    className={`px-16 py-3 whitespace-nowrap ${
                      activeTab === tab
                        ? "border-b-2 border-yellow-600 text-yellow-600 font-medium"
                        : isDisabled ? "text-gray-300 cursor-not-allowed" : "text-gray-500"
                    }`}
                    onClick={() => {
                      if (!isDisabled) {
                        if (tab === "Successful Booking Message") {
                          setActiveTab(tab); // Stay on current page
                        } else {
                          // Navigate to the corresponding page
                          router.push(tabRedirects[tab as keyof typeof tabRedirects]);
                        }
                      }
                    }}
                    disabled={isDisabled}
                  >
                    {tab}
                  </button>
                );
              })}
            </div>
          </div>

          <div className="p-6 md:p-8">
            <h2 className="text-xl font-semibold mb-6">
              Write A Message Which Your Client Will See After They Book A Session With You.
            </h2>

            {isDataLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader size="large" text="Loading message..." />
              </div>
            ) : (
              <>
                <div className="flex flex-col">
                  <textarea
                    className={`p-3 border w-[50%] h-auto ${error ? 'border-red-500' : 'border-gray-300'} rounded-lg min-h-[120px] text-sm text-primary focus:border-green-600 focus:outline-none`}
                    value={message}
                    onChange={handleMessageChange}
                    placeholder="Enter your booking success message"
                  ></textarea>
                  {error && (
                    <p className="text-red-500 text-sm mt-1">{error}</p>
                  )}
                </div>
              </>
            )}

            <div className="mt-8 text-start">
              <AnimatedButton
                onClick={handleSubmit}
                isLoading={isLoading}
                isSuccess={saveSuccess}
                disabled={isLoading || isDataLoading || saveSuccess || !message.trim()}
                loadingText="Saving..."
                successText="Saved!"
                className="px-4 sm:px-6 w-full sm:w-auto min-w-[150px] sm:min-w-[200px] h-[48px]"
              >
                Save Changes
              </AnimatedButton>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default BookingMessagePage;
