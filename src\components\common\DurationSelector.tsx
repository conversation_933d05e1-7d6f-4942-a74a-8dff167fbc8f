import React, { useState, useRef, useEffect } from 'react';
import { CaretDown } from '@phosphor-icons/react';
import ReactDOM from 'react-dom';

interface DurationSelectorProps {
  value: number; // Duration in minutes
  onChange: (duration: number) => void;
  className?: string;
}

const DurationSelector: React.FC<DurationSelectorProps> = ({
  value,
  onChange,
  className,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownPortalRef = useRef<HTMLDivElement>(null);
  const selectorRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const [selectedValue, setSelectedValue] = useState(value);

  // Available durations in minutes
  const durations = [15, 45, 50, 60];

  // Update internal state when prop changes
  useEffect(() => {
    // Validate the value to ensure it's within a reasonable range
    const validatedValue = validateDuration(value);
    setSelectedValue(validatedValue);
  }, [value]);

  // Validate duration to ensure it's within a reasonable range
  const validateDuration = (minutes: number): number => {
    // If the duration is less than 15 minutes, default to 15 minutes
    if (minutes < 15) {
      console.warn(`Duration ${minutes} minutes is too short, defaulting to 15 minutes`);
      return 15;
    }

    // If the duration is more than 4 hours (240 minutes), cap it at 4 hours
    if (minutes > 240) {
      console.warn(`Duration ${minutes} minutes is too long, capping at 4 hours (240 minutes)`);
      return 240;
    }

    // If the duration is not in the predefined list, find the closest value
    if (!durations.includes(minutes)) {
      // Find the closest duration in the predefined list
      const closestDuration = durations.reduce((prev, curr) => {
        return Math.abs(curr - minutes) < Math.abs(prev - minutes) ? curr : prev;
      });

      console.warn(`Duration ${minutes} minutes is not in the predefined list, using closest value: ${closestDuration} minutes`);
      return closestDuration;
    }

    return minutes;
  };

  // Format duration for display
  const formatDuration = (minutes: number): string => {
    // For durations less than 60 minutes, display in minutes
    if (minutes < 60) {
      return `${minutes} minutes`;
    }

    // For durations of 60 minutes or more, display in hours and minutes
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) {
      // If it's an exact number of hours
      return hours === 1 ? `1 hour` : `${hours} hours`;
    } else {
      // If there are remaining minutes
      const hourText = hours === 1 ? `1 hour` : `${hours} hours`;
      return `${hourText} ${remainingMinutes} min`;
    }
  };

  const handleSelectDuration = (duration: number) => {

    // Validate the duration
    const validatedDuration = validateDuration(duration);

    // Update internal state
    setSelectedValue(validatedDuration);

    // Call the parent's onChange handler
    onChange(validatedDuration);

    // Close the dropdown after a short delay to ensure the click event completes
    setTimeout(() => {
      setShowDropdown(false);
    }, 100);
  };

  const handleClickOutside = (event: MouseEvent) => {
    // Don't close if clicking on a dropdown item
    if (event.target instanceof Element) {
      const clickedOnDropdownItem = event.target.closest('.duration-dropdown-item');
      if (clickedOnDropdownItem) {
        return;
      }
    }

    // Close if clicking outside the selector and dropdown
    const clickedInDropdown = dropdownPortalRef.current && dropdownPortalRef.current.contains(event.target as Node);
    const clickedInSelector = dropdownRef.current && dropdownRef.current.contains(event.target as Node);

    if (!clickedInDropdown && !clickedInSelector) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Recalculate dropdown position when window is scrolled or resized
  useEffect(() => {
    if (showDropdown) {
      const handleScrollAndResize = () => {
        calculateDropdownPosition();
      };

      // Add event listeners for scroll and resize
      window.addEventListener('scroll', handleScrollAndResize, true);
      window.addEventListener('resize', handleScrollAndResize);

      // Find any parent modal elements that might be scrollable
      const parentModal = selectorRef.current?.closest('.modal-container');
      const timeSlotContainer = selectorRef.current?.closest('.time-slots-container');

      if (parentModal) {
        parentModal.addEventListener('scroll', handleScrollAndResize, true);
      }

      if (timeSlotContainer) {
        timeSlotContainer.addEventListener('scroll', handleScrollAndResize, true);
      }

      // Set up a periodic recalculation to handle any edge cases
      const intervalId = setInterval(handleScrollAndResize, 300);

      return () => {
        window.removeEventListener('scroll', handleScrollAndResize, true);
        window.removeEventListener('resize', handleScrollAndResize);

        if (parentModal) {
          parentModal.removeEventListener('scroll', handleScrollAndResize, true);
        }

        if (timeSlotContainer) {
          timeSlotContainer.removeEventListener('scroll', handleScrollAndResize, true);
        }

        clearInterval(intervalId);
      };
    }
  }, [showDropdown]);

  const calculateDropdownPosition = () => {
    if (selectorRef.current) {
      const rect = selectorRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      // For mobile screens, use a simpler, more direct positioning approach
      const isMobile = viewportWidth < 768;

      if (isMobile) {
        // On mobile, position dropdown directly under the selector field
        // regardless of scroll position or viewport boundaries

        // Set width to match selector width, but ensure it fits on screen
        const dropdownWidth = Math.min(rect.width, viewportWidth - 20);

        // Calculate left position to align with selector
        // Ensure it doesn't go off screen edges
        const leftPos = Math.max(10, Math.min(rect.left, viewportWidth - dropdownWidth - 10));

        // For top position, place it directly below the selector if possible
        // If selector is at bottom of screen, place dropdown above
        const dropdownHeight = 120;
        const spaceBelow = viewportHeight - rect.bottom;

        let top;
        if (spaceBelow >= 100) {
          // Enough space below - position dropdown below selector
          top = rect.bottom;
        } else {
          // Not enough space below - position dropdown above selector
          top = Math.max(10, rect.top - dropdownHeight);
        }

        setDropdownPosition({
          top,
          left: leftPos,
          width: dropdownWidth
        });
        return;
      }

      // For desktop, use direct positioning similar to mobile but with scrollX/Y offsets
      const dropdownHeight = 120;
      const spaceBelow = viewportHeight - rect.bottom;

      // Calculate left position to align with selector
      // For desktop, we need to account for scroll position
      const leftPos = Math.max(10, Math.min(rect.left, viewportWidth - rect.width - 10));

      // For top position, place it directly below the selector if possible
      // If selector is at bottom of screen, place dropdown above
      let top;
      if (spaceBelow >= 100) {
        // Enough space below - position dropdown below selector
        top = rect.bottom;
      } else {
        // Not enough space below - position dropdown above selector
        top = Math.max(10, rect.top - dropdownHeight);
      }

      setDropdownPosition({
        top,
        left: leftPos,
        width: rect.width
      });
    }
  };

  const handleToggleDropdown = () => {
    calculateDropdownPosition();
    setShowDropdown(!showDropdown);
  };

  return (
    <div className={`relative min-w-[120px] ${className}`} ref={dropdownRef}>
      <div
        ref={selectorRef}
        className="flex items-center justify-between w-full text-sm py-3 px-2.5 border rounded-lg outline-none cursor-pointer text-primary border-[#D9D9D9]"
        onClick={handleToggleDropdown}
        style={{ height: '42px' }} // Ensure consistent height with time pickers
      >
        <span>{formatDuration(selectedValue)}</span>
        <CaretDown size={16} className="text-gray-500" />
      </div>

      {showDropdown && typeof document !== 'undefined' && ReactDOM.createPortal(
        <div
          ref={dropdownPortalRef}
          className="fixed z-[999999] bg-white shadow-lg border border-gray-200 rounded-lg overflow-y-auto"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`,
            maxHeight: '120px',
            position: 'fixed',
            transform: 'translateZ(0)',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
          }}
        >
          <div className="py-1">
            {durations.map((durationOption) => (
              <button
                type="button"
                key={durationOption}
                onClick={() => handleSelectDuration(durationOption)}
                className={`duration-dropdown-item w-full text-left px-4 py-2 cursor-pointer hover:bg-gray-100 transition duration-200 text-sm ${
                  selectedValue === durationOption
                    ? "bg-blue-600/10 text-blue-600 font-medium"
                    : "text-primary"
                }`}
              >
                {formatDuration(durationOption)}
              </button>
            ))}
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default DurationSelector;
