"use client";
import React, { useState } from "react";
import Button from "@/components/common/Button";
import { SyncResult } from "@/services/webhook.service";
import { triggerEnhancedManualSync } from "@/services/setting.service";
import { Repeat, CheckCircle, XCircle, Warning } from "@phosphor-icons/react";
import toast from "react-hot-toast";

interface ManualSyncButtonProps {
  onSyncComplete?: (result: SyncResult) => void;
  className?: string;
  variant?: "filledGreen" | "outlinedGreen" | "filled" | "outlined";
  size?: "sm" | "md" | "lg";
  showLastResult?: boolean;
}

const ManualSyncButton: React.FC<ManualSyncButtonProps> = ({
  onSyncComplete,
  className = "",
  variant = "outlinedGreen",
  size = "md",
  showLastResult = true,
}) => {
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastResult, setLastResult] = useState<SyncResult | null>(null);
  const [showResult, setShowResult] = useState(false);

  const handleManualSync = async () => {
    setIsSyncing(true);
    setShowResult(false);

    try {
      // Use enhanced manual sync that handles rescheduled events
      const enhancedResult = await triggerEnhancedManualSync();

      // Convert enhanced result to SyncResult format for compatibility
      const result: SyncResult = {
        success: enhancedResult.success,
        syncedEvents: enhancedResult.updatedEvents,
        updatedEvents: enhancedResult.updatedEvents,
        createdEvents: 0,
        deletedEvents: 0,
        errors: enhancedResult.errors || [],
        conflicts: []
      };

      setLastResult(result);
      setShowResult(true);

      // Show appropriate success message
      if (enhancedResult.rescheduledEventsFound > 0) {
        toast.success(
          `✅ Updated ${enhancedResult.updatedEvents} rescheduled event${enhancedResult.updatedEvents !== 1 ? 's' : ''}!`,
          {
            icon: '🔄',
            style: {
              borderRadius: '10px',
              background: '#10B981',
              color: '#fff',
            },
          }
        );
      } else {
        toast.success(
          "✅ No rescheduled events found. All events are up to date!",
          {
            icon: '✅',
            style: {
              borderRadius: '10px',
              background: '#10B981',
              color: '#fff',
            },
          }
        );
      }

      // Auto-hide result after 10 seconds
      setTimeout(() => {
        setShowResult(false);
      }, 10000);

      if (onSyncComplete) {
        onSyncComplete(result);
      }

    } catch (error) {
      console.error("Enhanced manual sync failed:", error);
      toast.error("Manual sync failed. Please try again.");
    } finally {
      setIsSyncing(false);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "px-3 py-1.5 text-sm";
      case "lg":
        return "px-6 py-3 text-lg";
      default:
        return "px-4 py-2 text-base";
    }
  };

  const getResultIcon = () => {
    if (!lastResult) return null;
    
    if (lastResult.conflicts && lastResult.conflicts.length > 0) {
      return <Warning className="text-orange-500" size={16} />;
    }
    
    if (lastResult.errors && lastResult.errors.length > 0) {
      return <XCircle className="text-red-500" size={16} />;
    }
    
    return <CheckCircle className="text-green-500" size={16} />;
  };

  const getResultText = () => {
    if (!lastResult) return "";

    const { syncedEvents, updatedEvents, createdEvents, deletedEvents, conflicts, errors } = lastResult;

    if (conflicts && conflicts.length > 0) {
      return `Synced ${syncedEvents} events with ${conflicts.length} conflicts`;
    }

    if (errors && errors.length > 0) {
      return `Sync completed with ${errors.length} errors`;
    }

    const deletedText = deletedEvents > 0 ? `, ${deletedEvents} deleted` : '';
    return `Synced ${syncedEvents} events (${updatedEvents} updated, ${createdEvents} new${deletedText})`;
  };

  const getResultColor = () => {
    if (!lastResult) return "text-gray-600";
    
    if (lastResult.conflicts && lastResult.conflicts.length > 0) {
      return "text-orange-600";
    }
    
    if (lastResult.errors && lastResult.errors.length > 0) {
      return "text-red-600";
    }
    
    return "text-green-600";
  };

  return (
    <div className={`manual-sync-container ${className}`}>
      <Button
        onClick={handleManualSync}
        disabled={isSyncing}
        variant={variant}
        className={`flex items-center space-x-2 ${getSizeClasses()}`}
      >
        <Repeat 
          size={size === "sm" ? 14 : size === "lg" ? 20 : 16} 
          className={isSyncing ? "animate-spin" : ""} 
        />
        <span>
          {isSyncing ? "Syncing..." : "Manual Sync"}
        </span>
      </Button>
      
      {/* Last Sync Result */}
      {showLastResult && showResult && lastResult && (
        <div className="mt-3 p-3 bg-gray-50 border border-gray-200 rounded-md transition-all duration-300">
          <div className="flex items-start space-x-2">
            {getResultIcon()}
            <div className="flex-1">
              <p className={`text-sm font-medium ${getResultColor()}`}>
                {getResultText()}
              </p>
              
              {/* Detailed Results */}
              <div className="mt-2 text-xs text-gray-600 space-y-1">
                {lastResult.syncedEvents > 0 && (
                  <div className="flex justify-between">
                    <span>Events processed:</span>
                    <span className="font-medium">{lastResult.syncedEvents}</span>
                  </div>
                )}
                
                {lastResult.updatedEvents > 0 && (
                  <div className="flex justify-between">
                    <span>Events updated:</span>
                    <span className="font-medium text-blue-600">{lastResult.updatedEvents}</span>
                  </div>
                )}
                
                {lastResult.createdEvents > 0 && (
                  <div className="flex justify-between">
                    <span>New events created:</span>
                    <span className="font-medium text-green-600">{lastResult.createdEvents}</span>
                  </div>
                )}

                {lastResult.deletedEvents > 0 && (
                  <div className="flex justify-between">
                    <span>Events deleted:</span>
                    <span className="font-medium text-red-600">{lastResult.deletedEvents}</span>
                  </div>
                )}
                
                {lastResult.conflicts && lastResult.conflicts.length > 0 && (
                  <div className="flex justify-between">
                    <span>Conflicts detected:</span>
                    <span className="font-medium text-orange-600">{lastResult.conflicts.length}</span>
                  </div>
                )}
                
                {lastResult.errors && lastResult.errors.length > 0 && (
                  <div className="flex justify-between">
                    <span>Errors occurred:</span>
                    <span className="font-medium text-red-600">{lastResult.errors.length}</span>
                  </div>
                )}
              </div>
              
              {/* Conflicts Summary */}
              {lastResult.conflicts && lastResult.conflicts.length > 0 && (
                <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded text-xs">
                  <p className="font-medium text-orange-800 mb-1">Conflicts require attention:</p>
                  <ul className="space-y-1">
                    {lastResult.conflicts.slice(0, 3).map((conflict, index) => (
                      <li key={index} className="text-orange-700">
                        • {conflict.conflictType.replace('_', ' ')} - {conflict.suggestedResolution}
                      </li>
                    ))}
                    {lastResult.conflicts.length > 3 && (
                      <li className="text-orange-600 font-medium">
                        ... and {lastResult.conflicts.length - 3} more
                      </li>
                    )}
                  </ul>
                </div>
              )}
              
              {/* Errors Summary */}
              {lastResult.errors && lastResult.errors.length > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
                  <p className="font-medium text-red-800 mb-1">Errors occurred:</p>
                  <ul className="space-y-1">
                    {lastResult.errors.slice(0, 2).map((error, index) => (
                      <li key={index} className="text-red-700">
                        • {error.type}: {error.message}
                      </li>
                    ))}
                    {lastResult.errors.length > 2 && (
                      <li className="text-red-600 font-medium">
                        ... and {lastResult.errors.length - 2} more errors
                      </li>
                    )}
                  </ul>
                </div>
              )}
            </div>
            
            {/* Close Button */}
            <button
              onClick={() => setShowResult(false)}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Close result"
            >
              <XCircle size={16} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ManualSyncButton;
