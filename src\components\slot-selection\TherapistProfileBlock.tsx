"use client";

import { TherapistProfileData } from "@/types/slot-selection.types";
import { useRouter } from "next/navigation";

interface TherapistProfileBlockProps {
  therapistData: TherapistProfileData;
  isMobile?: boolean;
}

export default function TherapistProfileBlock({
  therapistData,
  isMobile = false,
}: TherapistProfileBlockProps) {
  const router = useRouter();

  const handleViewProfile = () => {
    // Navigate to the therapist profile page using the identifier
    router.push(`/clients/${therapistData.identifier}`);
  };
  if (isMobile) {
    return (
      <div className="bg-white p-4 flex items-center gap-4 border-b border-gray-200 min-h-[120px]">
        <img
          src={therapistData.profileImage}
          alt={therapistData.fullName}
          className="w-20 h-20 rounded-full object-cover"
        />
        <div className="flex-1">
          <h3 className="font-bold text-lg text-[#251D5C] mb-1">
            {therapistData.fullName}
            {therapistData.pronouns && (
              <span className="text-gray-600 font-normal">
                {" "}
                ({therapistData.pronouns})
              </span>
            )}
          </h3>
          <p className="text-sm text-gray-500 mb-1">
            {therapistData.designation}
          </p>
          <p className="text-sm text-[#27AE60] font-medium mb-3">
            {therapistData.experience}+ year of experience
          </p>
          <button
            onClick={handleViewProfile}
            className="bg-[#2C58BB] text-white rounded-full px-6 py-2 text-sm font-medium hover:bg-[#718FFF] transition w-full"
          >
            View Profile
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full md:w-1/3 bg-white mt-[50px] mr-[50px] rounded-xl shadow-md p-4 mx-2 flex flex-col items-center text-center justify-evenly">
      <img
        src={therapistData.profileImage}
        alt={therapistData.fullName}
        className="w-24 h-24 rounded-full object-cover mb-4"
      />
      <h3 className="font-bold text-lg text-[#251D5C]">
        {therapistData.fullName}
        {therapistData.pronouns && (
          <span className="text-gray-600 font-normal">
            {" "}
            ({therapistData.pronouns})
          </span>
        )}
      </h3>
      <p className="text-sm text-gray-500 mb-2">
        {therapistData.designation}
      </p>
      <p className="text-sm text-[#27AE60] font-medium mb-4">
        {therapistData.experience}+ year of experience
      </p>
      <button
        onClick={handleViewProfile}
        className="bg-[#2C58BB] text-white rounded-full px-6 py-2 text-sm font-medium hover:bg-[#718FFF] transition"
      >
        View Profile
      </button>
    </div>
  );
}
