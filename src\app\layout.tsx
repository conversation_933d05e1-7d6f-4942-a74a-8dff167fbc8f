import RouteProgressBar from "@/components/common/RouteProgressBar";
import { AuthProvider } from "@/context/AuthContext";
import { SubscriptionProvider } from "@/context/SubscriptionContext";
import { TherapistProvider } from "@/context/TherapistContext";
import GoogleAnalytics from "@/helper/GoogleAnalytics";
import type { Metadata } from "next";
import { Poppins } from "next/font/google";
import { Toaster } from "react-hot-toast";
import "./globals.css";
import HotJar from "@/helper/HotJar";
import Usetiful from "@/helper/Usetiful";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "Thought Pudding",
  description: "",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <GoogleAnalytics />
        <HotJar />
        <Usetiful token={"a0522d10b03dba6e442e240943c2fd14"} />
      </head>
      <body className={`${poppins.className} antialiased`}>
        {/* Google Analytics */}
        <RouteProgressBar />
        <Toaster
          position="top-center"
          toastOptions={{
            duration: 5000,
            style: {
              borderRadius: "10px",
              background: "#333",
              color: "#fff",
            },
          }}
          containerClassName="__toast-container"
        />
        <AuthProvider>
          <TherapistProvider>
            <SubscriptionProvider>{children}</SubscriptionProvider>
          </TherapistProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
