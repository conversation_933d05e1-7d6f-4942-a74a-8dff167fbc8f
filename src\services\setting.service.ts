import { useAuth } from "@/context/AuthContext";
import axiosInstance, { fetcher } from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import { useMemo } from "react";
import toast from "react-hot-toast";
import useS<PERSON> from "swr";
import { AxiosError } from "axios";

const swrOptions = {
  revalidateIfStale: false,
  revalidateOnFocus: false,
  revalidateOnReconnect: false,
};

const swrOptionsReSync = {
  revalidateIfStale: true,
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
};

interface Attendee {
  email: string;
  self?: boolean;
}

interface Event {
  id: string;
  summary: string;
  attendees?: Attendee[];
}


// get setting therapist data
export function useGetSettingData() {
  const auth = useAuth();

  const url =
    auth && auth.isAuthenticated
      ? `${endpoints.setting.therapistSettingData}`
      : null;

  const { data, isLoading, mutate: swrMutate } = useSWR(url, fetcher, swrOptions);

  const memoizedValue = useMemo(
    () => ({
      therapistData: data?.therapist,
      therapistLoading: isLoading,
      refetchTherapistData: () => swrMutate(),
    }),
    [data?.therapist, isLoading, swrMutate]
  );

  return memoizedValue;
}

// set setting therapist data
export async function updateSettingData(formData: Record<string, unknown>) {
  try {
    const url = `${endpoints.setting.therapistSettingData}`;
    const response = await axiosInstance.put(url, formData);
    if (response) toast.success("Update profile successfully!");
    return response;
  } catch (error) {
    return error;
  }
}

// get payment listing
export async function uploadProfileImage(upload: FormData) {
  const url = `${endpoints.setting.uploadProfile}`;

  const res = await axiosInstance.post(url, upload, {
    headers: { "Content-Type": "multipart/form-data" },
  });

  return res;
}

// therapist vpa upate
export async function therapistVpaUpdate(formData: Record<string, unknown>) {
  const url = `${endpoints.setting.therapistVpaUpdate}`;

  try {
    const res = await axiosInstance.put(url, formData);
    toast.success("Therapist VPA updated successfully!");
    return res;
  } catch (error) {
    toast.error(error?.toString() || "Failed to update Therapist VPA."); // Error toast
    throw error;
  }
}

// payment menu update
export async function paymentMenuUpdate(formData: Record<string, unknown>) {
  const url = `${endpoints.setting.paymentMenuUpdate}`;

  try {
    const res = await axiosInstance.post(url, formData);
    toast.success("Payment menu updated successfully!");
    return res;
  } catch (error) {
    toast.error(error?.toString() || "Failed to update payment menu."); // Error toast
    throw error;
  }
}

// get setting calendar sync data
export function useGetResyncCalendarData() {
  const query = `maxResults=${500}`;
  const url = `${endpoints.setting.calendarResync}?${query}`;

  const { data, isLoading } = useSWR(url, fetcher, swrOptions);

  const memoizedValue = useMemo(
    () => ({
      resyncCalendarData: data,
      resyncCalendarLoading: isLoading,
    }),
    [data, isLoading]
  );

  return memoizedValue;
}

export function useGetCalendarEvents() {
  const url = `${endpoints.setting.calendarResync}`;

  const { data, isLoading } = useSWR(url, fetcher, swrOptionsReSync);

  const memoizedValue = useMemo(
    () => ({
      resyncCalendarData: data,
      resyncCalendarLoading: isLoading,
    }),
    [data, isLoading]
  );

  return memoizedValue;
}

// Enhanced calendar events with new/rescheduled separation
export function useGetEnhancedCalendarEvents() {
  // Use the existing endpoint since it already returns the enhanced structure
  const url = `${endpoints.setting.calendarResync}`;

  const { data, isLoading } = useSWR(url, fetcher, swrOptionsReSync);

  const memoizedValue = useMemo(
    () => ({
      enhancedCalendarData: data,
      enhancedCalendarLoading: isLoading,
    }),
    [data, isLoading]
  );

  return memoizedValue;
}

type PaymentHistory = {
  data: {
    subscriptions: Array<unknown>;
    refetchPaymentHistory?: () => void;
  };
};

type subscriptionPlan = {
  data: {
    subscriptions: Array<unknown>;
  };
};
export function usePaymentHistory() {
  const url = `${endpoints.setting.subscription.paymentHistory}`;

  const { data, isLoading, mutate } = useSWR<PaymentHistory>(
    url,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      paymentHistoryData: data?.data?.subscriptions || [],
      paymentHistoryLoading: isLoading,
      refetchPaymentHistory: mutate,
    }),
    [data?.data?.subscriptions, isLoading, mutate]
  );

  return memoizedValue;
}

export async function eventCalendarSync({
  eventIds,
}: {
  eventIds: Array<string>;
}) {
  const url = `${endpoints.setting.eventSync}`;

  const res = await axiosInstance.post(url, { eventIds });

  return res;
}

export async function getSpecificCalendarEvents({
  eventIds,
}: {
  eventIds: Array<string>;
}) {
  try {
    const url = `${endpoints.setting.eventSync}`;
    const response = await axiosInstance.post(url, { eventIds });
    return response.data;
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    // If the error has a response with conflict data, return it
    if (axiosError.response && axiosError.response.status === 400 && axiosError.response.data) {
      return axiosError.response.data;
    }
    throw error;
  }
}

export async function updateSyncStatus() {
  const url = `${endpoints.setting.updateSyncStatus}`;
  const res = await axiosInstance.put(url);
  return res;
}

// Enhanced manual sync that handles rescheduled events
export async function triggerEnhancedManualSync() {
  try {
    // Step 1: Detect rescheduled events using resync API
    const resyncUrl = `${endpoints.setting.calendarResync}`;
    const resyncResponse = await axiosInstance.get(resyncUrl);
    const resyncData = resyncResponse.data;

    const rescheduledEvents = resyncData.rescheduled_events || [];

    // Step 2: Process rescheduled events if any found
    if (rescheduledEvents.length > 0) {
      const manualSyncUrl = `${endpoints.setting.webhook.manualSync}`;
      const manualSyncResponse = await axiosInstance.post(manualSyncUrl, {
        rescheduledEvents: rescheduledEvents,
        options: {
          incremental: true,
          maxResults: 100
        }
      });

      const result = manualSyncResponse.data;

      return {
        success: true,
        updatedEvents: result.updatedEvents || 0,
        processedRescheduledEvents: rescheduledEvents.length,
        rescheduledEventsFound: rescheduledEvents.length,
        errors: result.errors || []
      };
    } else {
      return {
        success: true,
        updatedEvents: 0,
        processedRescheduledEvents: 0,
        rescheduledEventsFound: 0,
        errors: []
      };
    }
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    console.error('Enhanced manual sync failed:', axiosError);
    throw error;
  }
}

export function useGetSubscriptionPlan() {
  const url = `${endpoints.setting.subscription.plan}`;

  const { data, isLoading } = useSWR<subscriptionPlan>(
    url,
    fetcher,
    swrOptions
  );
  const memoizedValue = useMemo(
    () => ({
      subscriptionPlanData: data?.data?.subscriptions || [],
      loading: isLoading,
    }),
    [data?.data?.subscriptions, isLoading]
  );

  return memoizedValue;
}

export async function activateFreeTrialSubscription(subscription_id: string) {
  const response = await axiosInstance.post(
    `${endpoints.setting.subscription.activateTrial}/${subscription_id}`
  );
  return response;
}


export async function createOrderFromSubscription(subscription_id: string) {
  const response = await axiosInstance.post(
    endpoints.setting.subscription.createOrder + "/" + subscription_id
  );
  return response;
}

export async function verifyPayment(payload: Record<string, unknown>) {
  const response = await axiosInstance.post(
    endpoints.setting.subscription.verifyPayment,
    payload
  );
  return response;
}

export async function checkTherapistSubscription() {
  const response = await axiosInstance.get(
    endpoints.setting.subscription.validTherapistSubscription
  );
  return response;
}

export async function createMultipleClients(clients: {
  name: string;
  defaultSessionAmount: string;
  email: string;
  phone: string;
}[]) {
  try {
    const payload = { clients };
    const response = await axiosInstance.post(
      endpoints.setting.createMultipleClients,
      payload
    );
    return response.data;
  } catch (error) {
    console.error("Update failed", error);
    throw error;
  }
}

export async function getCalendarEventClients(selectedEvents: Event[]) {
  try {
    const payload = {
      events: selectedEvents.map((event) => ({
        calendarEventId: event.id,
        summary: event.summary,
        attendee: event.attendees?.find((attend) => !attend.self)?.email || "",
      })),
    };

    const response = await axiosInstance.post(
      endpoints.setting.calendarEventClients,
      payload
    );

    return response.data.eventsWithClient;
  } catch (error) {
    console.error("Error fetching calendar event clients:", error);
    throw error;
  }
}
