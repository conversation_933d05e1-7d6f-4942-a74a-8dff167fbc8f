"use client";
import React, { useState, useEffect } from "react";
import DashboardLayout from "@/layout/dashboard/DashboardLayout";
import { tabs, tabRedirects } from "../tabs/tabsData";
import { useRouter } from "next/navigation";
import { isTabCompleted, markTabAsCompleted } from "@/utils/completedTabs";
import { Co<PERSON>, Eye, EyeSlash } from "@phosphor-icons/react";
import toast from "react-hot-toast";
import { fetchPersonalizationUrl } from "@/services/personalization.service";
import Loader from "@/components/common/Loader";
import AnimatedButton from "@/components/common/AnimatedButton";
import TransitionLoader from "@/components/common/TransitionLoader";

const PersonalizationPage = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("Personalization URL");
  const [urlValue, setUrlValue] = useState("");
  const [showUrl, setShowUrl] = useState(true);
  const [isCopied, setIsCopied] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isNavigating, setIsNavigating] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [redirectCountdown, setRedirectCountdown] = useState(0);

  // Fetch URL from backend
  useEffect(() => {
    const fetchUrl = async () => {
      try {
        setIsLoading(true);
        const response = await fetchPersonalizationUrl();
        setUrlValue(response.data[0].bookingURL);
      } catch (error) {
        console.error("Error fetching URL:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUrl();
  }, []);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(urlValue)
      .then(() => {
        setIsCopied(true);
        toast.success("Link copied to clipboard!");

        // Reset the copied state after 2 seconds
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      })
      .catch(err => {
        console.error("Failed to copy text: ", err);
        toast.error("Failed to copy link");
      });
  };

  const toggleUrlVisibility = () => {
    setShowUrl(!showUrl);
  };

  // Handle continue button click
  const handleContinue = () => {
    setIsNavigating(true);
    setSaveSuccess(true);

    // Mark this tab as completed
    markTabAsCompleted(activeTab);

    // Start countdown for redirection (3 seconds)
    setRedirectCountdown(3);

    // Set up countdown timer
    const countdownInterval = setInterval(() => {
      setRedirectCountdown(prev => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          setIsNavigating(false);

          // Navigate to next tab or page when countdown reaches 0
          const currentTabIndex = tabs.indexOf(activeTab);
          if (currentTabIndex < tabs.length - 1) {
            // Automatically redirect to the next tab
            router.push(tabRedirects[tabs[currentTabIndex + 1] as keyof typeof tabRedirects]);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Calculate progress percentage for the transition loader
  const calculateProgress = () => {
    if (!saveSuccess) return 0;
    return ((3 - redirectCountdown) / 3) * 100;
  };

  return (
    <DashboardLayout>
      {/* Transition loader - shown during redirection */}
      <TransitionLoader
        isVisible={saveSuccess}
        message="Great! Your URL is ready."
        redirectMessage="Redirecting to Booking Message..."
        progress={calculateProgress()}
      />
      <div className="p-0">
        {/* White background container with rounded corners */}
        <div className="bg-white rounded-xl shadow-sm">
          {/* Tabs */}
          <div className="mb-8">
            <div className="flex overflow-x-auto no-scrollbar mt-4">
              {tabs.map((tab, index) => {
                // Determine if this tab should be disabled
                const currentTabIndex = tabs.indexOf(activeTab);
                // Check if the tab is completed or is the current tab
                const isCompleted = isTabCompleted(tab);
                const isDisabled = index > currentTabIndex && !isCompleted;

                return (
                  <button
                    key={tab}
                    className={`px-16 sm:px-4 py-2 sm:py-3 text-sm sm:text-base whitespace-nowrap ${
                      activeTab === tab
                        ? "border-b-2 border-yellow-600 text-yellow-600 font-medium"
                        : isDisabled ? "text-gray-300 cursor-not-allowed" : "text-gray-500"
                    }`}
                    onClick={() => {
                      if (!isDisabled) {
                        if (tab === "Personalization URL") {
                          setActiveTab(tab); // Stay on current page
                        } else {
                          // Navigate to the corresponding page
                          router.push(tabRedirects[tab as keyof typeof tabRedirects]);
                        }
                      }
                    }}
                    disabled={isDisabled}
                  >
                    {tab}
                  </button>
                );
              })}
            </div>
          </div>

          <div className="p-4 sm:p-6 md:p-8">
            <div className="flex flex-col sm:flex-row sm:items-center mb-4">
              <h2 className="text-xl font-semibold">Setup Your Thought Pudding URL For Call</h2>
            </div>
            <p className="text-gray-600 mb-4 w-full sm:w-3/4 md:w-2/3 lg:w-1/2">
              Choose a URL that describes you or your practice in a concise way.
              Make it short and easy to remember so you can share links with ease.
            </p>

            {isLoading ? (
              <div className="flex justify-center items-center h-32 mt-6 mb-8">
                <Loader size="medium" text="Loading URL..." />
              </div>
            ) : (
              <>
                <div className="relative mt-6 mb-8">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center">
                    <div className="border border-gray-300 rounded-lg overflow-hidden w-full sm:w-[350px]" style={{ width: "50vh" }}>
                      <input
                        type="text"
                        value={showUrl ? urlValue : "•".repeat(urlValue.length)}
                        readOnly={true}
                        className="w-full py-3 px-4 outline-none bg-gray-50 text-gray-700"
                      />
                    </div>

                    <div className="flex items-center mt-2 sm:mt-0 sm:ml-2">
                      <button
                        onClick={toggleUrlVisibility}
                        className="p-2 text-gray-500 hover:text-gray-700 border border-gray-300 rounded-lg"
                        aria-label={showUrl ? "Hide URL" : "Show URL"}
                      >
                        {showUrl ? <Eye size={20} /> : <EyeSlash size={20} />}
                      </button>
                      <button
                        onClick={handleCopyLink}
                        className={`p-2 border rounded-lg ml-2 ${isCopied ? 'text-green-500 border-green-500' : 'text-gray-500 hover:text-gray-700 border-gray-300'}`}
                        aria-label="Copy URL"
                      >
                        <Copy size={20} />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="mt-8 text-start">
                  <AnimatedButton
                    onClick={handleContinue}
                    isLoading={isNavigating}
                    isSuccess={saveSuccess}
                    disabled={isNavigating || saveSuccess}
                    loadingText="Processing..."
                    successText="Redirecting..."
                    className="px-4 sm:px-6 w-full sm:w-auto min-w-[150px] sm:min-w-[200px] h-[48px]"
                  >
                    Continue
                  </AnimatedButton>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default PersonalizationPage;
