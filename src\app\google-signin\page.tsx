"use client";
import { useAuth } from "@/context/AuthContext";
import { AuthService } from "@/services/auth.service";
import axiosInstance from "@/utils/axios";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";

const GoogleSignIn = () => {
  const hasRun = useRef(false);
  const queryParams = new URLSearchParams(
    typeof window !== "undefined" ? window.location.search : ""
  );
  const router = useRouter();
  const { isAuthenticated, setIsAuthenticated } = useAuth() || {
    isAuthenticated: false,
  };

  const [error403, set403Error] = useState<boolean>(false);

  const code = queryParams.get("code");

  const scope = queryParams.get("scope");
  const errorQ = queryParams.get("error");

  const createTherapist = async () => {
    if (code && scope) {
      await AuthService.createTherapist(code, scope)
        .then((res) => {
          if (res.status == 200) {
            if (res?.data?.newUser || res?.data?.therapist_name === "") {
              sessionStorage.setItem("tempAuthKey", res.data.token);
              router.push(`/?q=${res?.data?.therapistId}&step=${2}`);
            } else if (res.data?.isVerified) {
              localStorage.setItem("authKeyTh", res.data.token);
              axiosInstance.defaults.headers.Authorization = res.data.token;
              if (setIsAuthenticated) setIsAuthenticated(true);

              toast.success("You are successfully logged in");
              router.push("/dashboard");
            } else if (!res.data.isVerified) {
              sessionStorage.setItem("tempAuthKey", res.data.token);
              router.push(`/?q=${res?.data?.therapistId}&step=${3}`);
            }
          }
        })
        .catch((e) => {
          if (e?.response?.status == 403) {
            set403Error(true);
          } else {
            console.error(e, "error while google signup!");
            if (e?.error) {
              toast.error(e.error);
            }
            // router.push('/login');
          }
        });
    }
  };

  useEffect(() => {
    if (!hasRun.current) {
      hasRun.current = true;
      createTherapist();
    }
  }, []);

  useEffect(() => {
    if (errorQ == "access_denied") {
      router.push("/");
    }
  }, [errorQ, router]);

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace("/");
    }
  }, [isAuthenticated, router]);

  if (error403) {
    return (
      <div className="mt-3">
        <div>
          <div className="text-center">
            <h6>Access Denied</h6>
            <div className="mt-4">
              {/* <img src={icon} height={'150px'} /> */}
            </div>
            <div className="mt-3">
              <span>
                Hi there! We&apos;re excited to have you onboard, fill up this
                form and we&apos;ll get back to you via email.
                <br />
                If you&apos;ve already filled the form, we&apos;ve sent you an
                email! <a href="https://bit.ly/thoughtpudding"> Form</a>{" "}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }
  return (
    <>
      {/* <h2>
                Google Signup 
            </h2>
            <div style={{marginTop: 10}}>
                <button onClick={createTherapist}>
                    Back To Dashboard
                </button>
            </div> */}

      <div className="flex item-center justify-center mt-5">
        {/* <Spinner /> */}
        <p className="">Loading...</p>
        {errorQ == "access_denied" ? (
          <h2 className="ms-2 text-center">Redirecting you to Login Page</h2>
        ) : (
          <h2 className="ms-2 text-center">Verification Start</h2>
        )}
      </div>
    </>
  );
};

export default GoogleSignIn;
