"use client";
import Footer from "@/app/Landing Page/Footer";
import SplashScreen from "@/components/common/SplashScreen";
import { useAuth } from "@/context/AuthContext";
import { usePathname, useRouter } from "next/navigation";
import React, { lazy, Suspense, useEffect } from "react";

const Header = lazy(() => import("./Header"));

const WebsiteLayout: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const router = useRouter();
  const pathname = usePathname(); // Get the current path
  const { isAuthenticated } = useAuth() || { isAuthenticated: false };

  useEffect(() => {
    if (isAuthenticated && pathname === "/") {
      router.push("/dashboard");
    }
  }, [isAuthenticated, pathname, router]);
  return (
    <Suspense fallback={<SplashScreen />}>
      <div className="h-full bg-white">
        <Header />
        <div>
          <div>{children}</div>
        </div>
        <div className="lg:px-[150px] lg:py-[40px] md:px-[75px] md:py-[20px] px-4 py-2">
          <Footer />
        </div>
      </div>
    </Suspense>
  );
};

export default WebsiteLayout;
