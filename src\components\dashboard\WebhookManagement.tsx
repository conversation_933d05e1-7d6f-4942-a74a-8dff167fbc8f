"use client";
import React, { useState, useEffect, useCallback } from "react";
import Button from "@/components/common/Button";
import {
  setupWebhook,
  stopWebhook,
  renewWebhook,
  useGetWebhookStatus,
  WebhookWebSocket
} from "@/services/webhook.service";
import { Repeat, CheckCircle, XCircle } from "@phosphor-icons/react";
import moment from "moment";
import toast from "react-hot-toast";

interface WebhookManagementProps {
  therapistId: string;
}

const WebhookManagement: React.FC<WebhookManagementProps> = ({ therapistId }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [wsConnection, setWsConnection] = useState<WebhookWebSocket | null>(null);

  const {
    webhookStatus,
    webhookLoading,
    refreshWebhookStatus
  } = useGetWebhookStatus();

  const hasActiveWebhook = webhookStatus?.hasActiveWebhook || false;
  const activeWebhook = webhookStatus?.webhooks?.[0];

  const handleRealTimeUpdate = useCallback((data: { type: string; [key: string]: unknown }) => {
    switch (data.type) {
      case 'SYNC_COMPLETED':
        toast.success("Calendar synced automatically!", {
          icon: '✅',
          style: {
            borderRadius: '10px',
            background: '#10B981',
            color: '#fff',
          },
        });
        break;
      case 'CONFLICT_DETECTED':
        toast.error("Schedule conflict detected. Please review your calendar.", {
          icon: '⚠️',
          style: {
            borderRadius: '10px',
            background: '#EF4444',
            color: '#fff',
          },
        });
        break;
      case 'WEBHOOK_EXPIRED':
        refreshWebhookStatus();
        toast.error("Real-time sync expired. Please renew.", {
          icon: '⏰',
          style: {
            borderRadius: '10px',
            background: '#F59E0B',
            color: '#fff',
          },
        });
        break;
      case 'SESSION_RESCHEDULED':
        toast.success("Session rescheduled from Google Calendar", {
          icon: '📅',
          style: {
            borderRadius: '10px',
            background: '#10B981',
            color: '#fff',
          },
        });
        break;
      case 'SESSION_DELETED':
        toast.success("Session deleted from Google Calendar", {
          icon: '🗑️',
          style: {
            borderRadius: '10px',
            background: '#10B981',
            color: '#fff',
          },
        });
        break;
      case 'SERIES_RESCHEDULED':
        toast.success("Session series rescheduled from Google Calendar", {
          icon: '📅',
          style: {
            borderRadius: '10px',
            background: '#10B981',
            color: '#fff',
          },
        });
        break;
      case 'SYNC_ERROR':
        toast.error("Sync error occurred. Please check your calendar.", {
          icon: '❌',
          style: {
            borderRadius: '10px',
            background: '#EF4444',
            color: '#fff',
          },
        });
        break;
    }
  }, [refreshWebhookStatus]);

  // Setup WebSocket connection when webhook is active
  useEffect(() => {
    const enableWebSocket = process.env.NEXT_PUBLIC_ENABLE_WEBSOCKET === 'true';

    if (hasActiveWebhook && !wsConnection && enableWebSocket) {
      const ws = new WebhookWebSocket(therapistId, handleRealTimeUpdate);
      ws.connect();
      setWsConnection(ws);
    } else if (!hasActiveWebhook && wsConnection) {
      wsConnection.disconnect();
      setWsConnection(null);
    }

    return () => {
      if (wsConnection) {
        wsConnection.disconnect();
      }
    };
  }, [hasActiveWebhook, therapistId, wsConnection, handleRealTimeUpdate]);

  const handleSetupWebhook = async () => {
    setIsLoading(true);
    try {
      await setupWebhook();
      setTimeout(async () => {
        await refreshWebhookStatus();
      }, 1000);
    } catch {
      // Error handling is done in the service
    } finally {
      setIsLoading(false);
    }
  };

  const handleStopWebhook = async () => {
    if (!activeWebhook) return;
    setIsLoading(true);
    try {
      await stopWebhook();
      await refreshWebhookStatus();
    } catch {
      // Error handling is done in the service
    } finally {
      setIsLoading(false);
    }
  };

  const handleRenewWebhook = async () => {
    if (!activeWebhook) return;
    setIsLoading(true);
    try {
      await renewWebhook();
      await refreshWebhookStatus();
    } catch {
      // Error handling is done in the service
    } finally {
      setIsLoading(false);
    }
  };



  const isWebhookExpired = () => {
    if (!activeWebhook) return false;
    const expirationTime = new Date(activeWebhook.expiration);
    const now = new Date();
    return now >= expirationTime;
  };

  if (webhookLoading) {
    return (
      <div className="webhook-panel bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
          <div className="h-8 bg-gray-300 rounded w-full"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="webhook-panel bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Calendar Sync</h3>
        <div className="flex items-center space-x-2">
          <div
            className={`w-2 h-2 rounded-full ${
              hasActiveWebhook && !isWebhookExpired() ? 'bg-green-500' : 'bg-red-500'
            }`}
          />
          <span className={`text-sm font-medium ${
            hasActiveWebhook && !isWebhookExpired() ? 'text-green-700' : 'text-red-700'
          }`}>
            {hasActiveWebhook && !isWebhookExpired() ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      {/* Sync Status */}
      {hasActiveWebhook && activeWebhook && !isWebhookExpired() && (
        <div className="bg-white border border-gray-200 rounded-md p-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Real-time sync enabled</span>
            <span className="text-sm font-medium text-green-600">Connected</span>
          </div>
          {activeWebhook.syncStatistics?.lastSyncAt && (
            <div className="flex items-center justify-between mt-2">
              <span className="text-sm text-gray-600">Last sync:</span>
              <span className="text-sm font-medium">
                {moment(activeWebhook.syncStatistics.lastSyncAt).fromNow()}
              </span>
            </div>
          )}
          {/* {activeWebhook.syncStatistics && (
            <div className="flex items-center justify-between mt-2">
              <span className="text-sm text-gray-600">Total syncs:</span>
              <span className="text-sm font-medium">
                {activeWebhook.syncStatistics.totalSyncs}
              </span>
            </div>
          )} */}
        </div>
      )}

      {/* Expired Status */}
      {hasActiveWebhook && isWebhookExpired() && (
        <div className="bg-orange-50 border border-orange-200 rounded-md p-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-orange-800">Sync Expired</span>
            <span className="text-sm text-orange-600">
              Expired {moment(activeWebhook?.expiration).fromNow()}
            </span>
          </div>
          <p className="text-sm text-orange-700 mt-1">
            Real-time sync has expired. Please renew to continue automatic updates.
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-3">
        {!hasActiveWebhook ? (
          <Button
            onClick={handleSetupWebhook}
            disabled={isLoading}
            variant="filledGreen"
            className="flex items-center space-x-2"
          >
            <CheckCircle size={16} />
            <span>{isLoading ? "Enabling..." : "Enable Real-time Sync"}</span>
          </Button>
        ) : (
          <>
            {isWebhookExpired() ? (
              <Button
                onClick={handleRenewWebhook}
                disabled={isLoading}
                variant="filledGreen"
                className="flex items-center space-x-2"
              >
                <Repeat size={16} />
                <span>{isLoading ? "Renewing..." : "Renew Sync"}</span>
              </Button>
            ) : (
              <Button
                onClick={handleStopWebhook}
                disabled={isLoading}
                variant="outlinedRed"
                className="flex items-center space-x-2"
              >
                <XCircle size={16} />
                <span>{isLoading ? "Disabling..." : "Disable Sync"}</span>
              </Button>
            )}
          </>
        )}
      </div>

    </div>
  );
};

export default WebhookManagement;
