import axiosInstance, { fetcher } from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import useSWR from "swr";
import { useMemo } from "react";
import toast from "react-hot-toast";

// TypeScript interfaces for webhook data
export interface WebhookStatus {
  _id: string;
  therapistId: string;
  channelId: string;
  resourceId: string;
  expiration: string;
  isActive: boolean;
  syncStatistics: {
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    eventsProcessed: number;
    conflictsDetected: number;
    conflictsResolved: number;
    lastSyncAt?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface SyncResult {
  success: boolean;
  syncedEvents: number;
  updatedEvents: number;
  createdEvents: number;
  deletedEvents: number;
  errors: Array<{
    type: string;
    message: string;
    timestamp: string;
  }>;
  conflicts: Array<{
    eventId: string;
    conflictType: 'time_overlap' | 'double_booking';
    conflictingWith: string[];
    suggestedResolution: string;
  }>;
}

export interface SyncStatistics {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  eventsProcessed: number;
  conflictsDetected: number;
  conflictsResolved: number;
  lastSyncAt?: string;
  averageSyncTime: string;
  syncFrequency: string;
  webhookHealth: string;
}

export interface WebhookSetupResponse {
  success: boolean;
  message: string;
  data: {
    channelId: string;
    resourceId: string;
    expiration: string;
    webhookUrl: string;
  };
  status: number;
}

export interface WebhookStatusResponse {
  success: boolean;
  message: string;
  data: {
    webhooks: WebhookStatus[];
    count: number;
    hasActiveWebhook: boolean;
  };
  status: number;
}

// Setup webhook for real-time sync
export async function setupWebhook(): Promise<WebhookSetupResponse> {
  try {
    const url = `${endpoints.setting.webhook.setup}`;
    const response = await axiosInstance.post(url);

    // Let axios interceptor handle success toast
    if (response.data.success) {
      return response.data;
    } else {
      throw new Error(response.data.message || "Webhook setup failed");
    }
  } catch (error: unknown) {
    // Let axios interceptor handle error toast
    throw error;
  }
}

// Get webhook status
export function useGetWebhookStatus() {
  const url = `${endpoints.setting.webhook.status}`;

  const { data, isLoading, error, mutate } = useSWR<WebhookStatusResponse>(
    url,
    fetcher,
    {
      refreshInterval: 30000, // Refresh every 30 seconds
      revalidateOnFocus: true,
    }
  );

  const memoizedValue = useMemo(
    () => ({
      webhookStatus: data?.data || null,
      webhookLoading: isLoading,
      webhookError: error,
      refreshWebhookStatus: mutate,
    }),
    [data, isLoading, error, mutate]
  );

  return memoizedValue;
}

// Stop webhook
export async function stopWebhook(): Promise<void> {
  try {
    const url = `${endpoints.setting.webhook.stop}`;
    await axiosInstance.post(url); // No body needed - backend finds webhook automatically
    // Let axios interceptor handle success toast
  } catch (error) {
    console.error("Error stopping webhook:", error);
    // Let axios interceptor handle error toast
    throw error;
  }
}

// Renew webhook
export async function renewWebhook(): Promise<WebhookSetupResponse> {
  try {
    const url = `${endpoints.setting.webhook.renew}`;
    const response = await axiosInstance.post(url); // No body needed - backend finds webhook automatically

    // Let axios interceptor handle success toast
    return response.data;
  } catch (error) {
    // Let axios interceptor handle error toast
    throw error;
  }
}

// Trigger manual sync
export async function triggerManualSync(options: {
  incremental?: boolean;
  maxResults?: number;
  timeMin?: string;
  timeMax?: string;
} = {}): Promise<SyncResult> {
  const toastId = toast.loading("Syncing calendar events...", {
    duration: Infinity,
  });

  try {
    const url = `${endpoints.setting.webhook.manualSync}`;
    const requestBody = {
      options: {
        incremental: options.incremental ?? true,
        maxResults: options.maxResults ?? 100,
        ...(options.timeMin && { timeMin: options.timeMin }),
        ...(options.timeMax && { timeMax: options.timeMax }),
      }
    };

    const response = await axiosInstance.post(url, requestBody);

    toast.dismiss(toastId);

    if (response.data.success) {
      const result = response.data.data;
      const deletedText = result.deletedEvents > 0 ? `, ${result.deletedEvents} deleted` : '';
      toast.success(
        `Sync completed! ${result.syncedEvents} events processed, ${result.updatedEvents} updated, ${result.createdEvents} created${deletedText}.`,
        {
          icon: '✅',
          style: {
            borderRadius: '10px',
            background: '#10B981',
            color: '#fff',
          },
        }
      );
      return result;
    } else {
      toast.error("Sync completed with errors. Please check the results.", {
        icon: '❌',
        style: {
          borderRadius: '10px',
          background: '#EF4444',
          color: '#fff',
        },
      });
      return response.data.data;
    }
  } catch (error) {
    toast.dismiss(toastId);
    toast.error("Manual sync failed. Please try again.", {
      icon: '❌',
      style: {
        borderRadius: '10px',
        background: '#EF4444',
        color: '#fff',
      },
    });
    throw error;
  }
}

// Get sync statistics
export function useGetSyncStatistics() {
  const url = `${endpoints.setting.webhook.statistics}`;

  const { data, isLoading, error, mutate } = useSWR<{
    success: boolean;
    data: SyncStatistics;
  }>(
    url,
    fetcher,
    {
      refreshInterval: 60000, // Refresh every minute
      revalidateOnFocus: true,
    }
  );

  const memoizedValue = useMemo(
    () => ({
      syncStatistics: data?.data || null,
      syncStatsLoading: isLoading,
      syncStatsError: error,
      refreshSyncStats: mutate,
    }),
    [data, isLoading, error, mutate]
  );

  return memoizedValue;
}

// Get calendar events with sync status
export async function getCalendarEvents(
  startDate?: string,
  endDate?: string,
  includeDeleted: boolean = false,
  syncStatus: string = 'all'
) {
  try {
    const url = `${endpoints.sessions.schedules}/calendar/events`;
    const params = new URLSearchParams();

    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    params.append('includeDeleted', includeDeleted.toString());
    params.append('syncStatus', syncStatus);

    const response = await axiosInstance.get(`${url}?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching calendar events:", error);
    throw error;
  }
}

// Handle Google Calendar series reschedule
export async function handleCalendarSeriesReschedule(
  calendarSeriesId: string,
  calendarEventId: string,
  newStartTime: string,
  newEndTime: string,
  rescheduleType: 'entire_series' | 'partial_series' | 'single_session'
) {
  try {
    const url = `${endpoints.sessions.rescheduleCalendarSeries}`;
    const requestData = {
      calendarSeriesId,
      calendarEventId,
      newStartTime,
      newEndTime,
      rescheduleType
    };

    const response = await axiosInstance.put(url, requestData);
    return response.data;
  } catch (error) {
    console.error("Error handling calendar series reschedule:", error);
    throw error;
  }
}

// WebSocket connection for real-time updates
export class WebhookWebSocket {
  private ws: WebSocket | null = null;
  private therapistId: string;
  private onMessage: (data: { type: string; [key: string]: unknown }) => void;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor(therapistId: string, onMessage: (data: { type: string; [key: string]: unknown }) => void) {
    this.therapistId = therapistId;
    this.onMessage = onMessage;
  }

  connect() {
    try {
      // Use environment variable or fallback to backend server
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL ||
        `ws://localhost:5959/ws/sync/${this.therapistId}`;

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.onMessage(data);
        } catch {
          // Silently handle parsing errors
        }
      };

      this.ws.onclose = () => {
        this.attemptReconnect();
      };

      this.ws.onerror = () => {
        // Don't attempt reconnection if WebSocket server doesn't exist
        this.reconnectAttempts = this.maxReconnectAttempts;
      };
    } catch {
      // Silently handle connection errors
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}