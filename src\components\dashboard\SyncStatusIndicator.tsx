"use client";
import React, { useState, useEffect } from "react";
import { 
  useGetWebhookStatus,
  useGetSyncStatistics,
  WebhookWebSocket
} from "@/services/webhook.service";
import { CheckCircle, XCircle, Warning, Repeat } from "@phosphor-icons/react";
import moment from "moment";

interface SyncStatusIndicatorProps {
  therapistId: string;
  className?: string;
}

const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({ 
  therapistId, 
  className = "" 
}) => {
  const [syncInProgress, setSyncInProgress] = useState(false);
  const [hasConflicts, setHasConflicts] = useState(false);
  const [wsConnection, setWsConnection] = useState<WebhookWebSocket | null>(null);
  const [lastActivity, setLastActivity] = useState<string | null>(null);

  const { 
    webhookStatus, 
    webhookLoading 
  } = useGetWebhookStatus();

  const { 
    syncStatistics 
  } = useGetSyncStatistics();

  const isWebhookActive = webhookStatus?.hasActiveWebhook || false;
  const activeWebhook = webhookStatus?.webhooks?.[0];

  // Setup WebSocket connection when webhook is active
  useEffect(() => {
    if (isWebhookActive && !wsConnection) {
      const ws = new WebhookWebSocket(therapistId, handleRealTimeUpdate);
      ws.connect();
      setWsConnection(ws);
    } else if (!isWebhookActive && wsConnection) {
      wsConnection.disconnect();
      setWsConnection(null);
    }

    return () => {
      if (wsConnection) {
        wsConnection.disconnect();
      }
    };
  }, [isWebhookActive, therapistId, wsConnection]);

  const handleRealTimeUpdate = (data: { type: string; [key: string]: unknown }) => {
    setLastActivity(new Date().toISOString());
    
    switch (data.type) {
      case 'SYNC_STARTED':
        setSyncInProgress(true);
        break;
        
      case 'SYNC_COMPLETED':
        setSyncInProgress(false);
        setHasConflicts(false);
        break;
        
      case 'CONFLICT_DETECTED':
        setHasConflicts(true);
        setSyncInProgress(false);
        break;
        
      case 'SYNC_ERROR':
        setSyncInProgress(false);
        break;
    }
  };

  const getStatusColor = () => {
    if (!isWebhookActive) return "text-gray-400";
    if (syncInProgress) return "text-blue-500";
    if (hasConflicts) return "text-orange-500";
    return "text-green-500";
  };

  const getStatusIcon = () => {
    if (!isWebhookActive) return <XCircle size={16} className="text-gray-400" />;
    if (syncInProgress) return <Repeat size={16} className="text-blue-500 animate-spin" />;
    if (hasConflicts) return <Warning size={16} className="text-orange-500" />;
    return <CheckCircle size={16} className="text-green-500" />;
  };

  const getStatusText = () => {
    if (webhookLoading) return "Loading...";
    if (!isWebhookActive) return "Sync Disabled";
    if (syncInProgress) return "Syncing...";
    if (hasConflicts) return "Conflicts";
    return "Active";
  };

  const getLastSyncText = () => {
    if (lastActivity) {
      return `Active ${moment(lastActivity).fromNow()}`;
    }
    if (syncStatistics?.lastSyncAt) {
      return `Synced ${moment(syncStatistics.lastSyncAt).fromNow()}`;
    }
    if (activeWebhook?.updatedAt) {
      return `Active ${moment(activeWebhook.updatedAt).fromNow()}`;
    }
    return "No recent activity";
  };

  const isExpiringSoon = () => {
    if (!activeWebhook) return false;
    const expirationTime = new Date(activeWebhook.expiration);
    const now = new Date();
    const hoursUntilExpiration = (expirationTime.getTime() - now.getTime()) / (1000 * 60 * 60);
    return hoursUntilExpiration < 24;
  };

  if (webhookLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="animate-pulse">
          <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
        </div>
        <span className="text-sm text-gray-500">Loading...</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Status Indicator with Pulse Animation */}
      <div className="relative">
        {getStatusIcon()}
        {isWebhookActive && !syncInProgress && (
          <div className="absolute -top-1 -right-1">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          </div>
        )}
        {isExpiringSoon() && (
          <div className="absolute -top-1 -right-1">
            <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
          </div>
        )}
      </div>

      {/* Status Text */}
      <div className="flex flex-col">
        <span className={`text-sm font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
        <span className="text-xs text-gray-500">
          {getLastSyncText()}
        </span>
      </div>

      {/* Conflict Badge */}
      {hasConflicts && (
        <div className="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-1 rounded-full">
          Conflicts
        </div>
      )}

      {/* Expiration Warning */}
      {isExpiringSoon() && (
        <div className="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-1 rounded-full">
          Expires Soon
        </div>
      )}
    </div>
  );
};

export default SyncStatusIndicator;
