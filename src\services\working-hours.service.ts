import axiosInstance from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import { AxiosError } from "axios";
import {
  TimeSlot,
  addDurationToTimeSlots,
  fetchSpecificWorkingHours,
  saveSpecificWorkingHours,
  saveSpecificWorkingHour,
  updateSpecificWorkingHour,
  deleteSpecificWorkingHourSlot,
  deleteSpecificWorkingHour,
  ApiResponse
} from "./specific-working-hours.service";

// Define the working hours data interface
export type { TimeSlot } from "./specific-working-hours.service";

// Define interfaces for working hours API
export interface WeeklyHoursData {
  sunday: TimeSlot[];
  monday: TimeSlot[];
  tuesday: TimeSlot[];
  wednesday: TimeSlot[];
  thursday: TimeSlot[];
  friday: TimeSlot[];
  saturday: TimeSlot[];
  _id?: string;
}

export interface SpecificDay {
  _id?: string;
  date: string;
  slots: TimeSlot[];
}

// Re-export functions from specific-working-hours.service
export {
  fetchSpecificWorkingHours,
  updateSpecificWorkingHour,
  saveSpecificWorkingHour,
  deleteSpecificWorkingHourSlot,
  deleteSpecificWorkingHour
};

export interface DaySchedule {
  isAvailable: boolean;
  timeSlots: TimeSlot[];
}

export interface WorkingHoursData {
  weeklyHours: {
    [key: string]: DaySchedule;
  };
  specificDays: {
    date: string;
    timeSlots: TimeSlot[];
  }[];
}

// Transform weekly hours data for backend
const transformWeeklyHoursForBackend = (weeklyHours: WorkingHoursData['weeklyHours']) => {
  return {
    sunday: weeklyHours.Sun.isAvailable ? addDurationToTimeSlots(weeklyHours.Sun.timeSlots) : [],
    monday: weeklyHours.Mon.isAvailable ? addDurationToTimeSlots(weeklyHours.Mon.timeSlots) : [],
    tuesday: weeklyHours.Tue.isAvailable ? addDurationToTimeSlots(weeklyHours.Tue.timeSlots) : [],
    wednesday: weeklyHours.Wed.isAvailable ? addDurationToTimeSlots(weeklyHours.Wed.timeSlots) : [],
    thursday: weeklyHours.Thu.isAvailable ? addDurationToTimeSlots(weeklyHours.Thu.timeSlots) : [],
    friday: weeklyHours.Fri.isAvailable ? addDurationToTimeSlots(weeklyHours.Fri.timeSlots) : [],
    saturday: weeklyHours.Sat.isAvailable ? addDurationToTimeSlots(weeklyHours.Sat.timeSlots) : []
  };
};



// Transform backend data to match frontend schema
const transformDataForFrontend = (data: WeeklyHoursData & { specificDays?: SpecificDay[] }): WorkingHoursData => {
  if (!data) {
    // Return default working hours if no data
    return getDefaultWorkingHours();
  }

  try {
    const {
      sunday = [],
      monday = [],
      tuesday = [],
      wednesday = [],
      thursday = [],
      friday = [],
      saturday = [],
      specificDays = []
    } = data;

    return {
      weeklyHours: {
        Sun: { isAvailable: sunday.length > 0, timeSlots: sunday },
        Mon: { isAvailable: monday.length > 0, timeSlots: monday },
        Tue: { isAvailable: tuesday.length > 0, timeSlots: tuesday },
        Wed: { isAvailable: wednesday.length > 0, timeSlots: wednesday },
        Thu: { isAvailable: thursday.length > 0, timeSlots: thursday },
        Fri: { isAvailable: friday.length > 0, timeSlots: friday },
        Sat: { isAvailable: saturday.length > 0, timeSlots: saturday }
      },
      specificDays: specificDays.map((day) => ({
        _id: day._id, // Preserve the _id field
        date: day.date,
        timeSlots: day.slots || []
      }))
    };
  } catch (error) {
    console.error("Error transforming data:", error);
    return getDefaultWorkingHours();
  }
};

// Get default working hours
const getDefaultWorkingHours = (): WorkingHoursData => {
  return {
    weeklyHours: {
      Sun: { isAvailable: false, timeSlots: [] },
      Mon: { isAvailable: true, timeSlots: [{ startTime: "08:00", endTime: "08:15", duration: 15 }] },
      Tue: { isAvailable: true, timeSlots: [{ startTime: "08:00", endTime: "08:15", duration: 15 }] },
      Wed: { isAvailable: true, timeSlots: [{ startTime: "08:00", endTime: "08:15", duration: 15 }] },
      Thu: { isAvailable: true, timeSlots: [{ startTime: "08:00", endTime: "08:15", duration: 15 }] },
      Fri: { isAvailable: true, timeSlots: [{ startTime: "08:00", endTime: "08:15", duration: 15 }] },
      Sat: { isAvailable: false, timeSlots: [] }
    },
    specificDays: []
  };
};

// Save working hours data (POST method)
export const saveWorkingHours = async (workingHoursData: WorkingHoursData): Promise<ApiResponse<WeeklyHoursData>> => {
  try {
    // First, save weekly hours
    const weeklyUrl = `${endpoints.workingHours}`;
    const weeklyData = transformWeeklyHoursForBackend(workingHoursData.weeklyHours);

    // Make sure all time slots have duration
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    days.forEach(day => {
      if (Array.isArray(weeklyData[day as keyof typeof weeklyData])) {
        const slots = weeklyData[day as keyof typeof weeklyData];
        slots.forEach((slot: TimeSlot) => {
          if (!slot.duration) {
            // Calculate duration if missing
            const startParts = slot.startTime.split(':').map(Number);
            const endParts = slot.endTime.split(':').map(Number);
            const startMinutes = startParts[0] * 60 + startParts[1];
            const endMinutes = endParts[0] * 60 + endParts[1];
            slot.duration = endMinutes > startMinutes ? endMinutes - startMinutes : (endMinutes + 24 * 60) - startMinutes;
          }
        });
      }
    });

    const weeklyResponse = await axiosInstance.post<ApiResponse<WeeklyHoursData>>(weeklyUrl, weeklyData);

    // Only proceed with specific days if weekly hours were saved successfully
    // AND there are actually specific days to save
    if (workingHoursData.specificDays && workingHoursData.specificDays.length > 0) {
      try {
        await saveSpecificWorkingHours(workingHoursData.specificDays);
      } catch (specificError) {
        console.error("Error saving specific days:", specificError);
        // Continue even if specific days fail
      }
    }

    return weeklyResponse.data;
  } catch (err) {
    console.error("Error saving working hours:", err);

    // Log more detailed error information
    if (err && typeof err === 'object' && 'response' in err) {
      const axiosError = err as AxiosError;
      if (axiosError.response) {
        console.error("Error response data:", axiosError.response.data);
        console.error("Error response status:", axiosError.response.status);
      }
    }

    throw err;
  }
};



// Update working hours data (PATCH method)
export const updateWorkingHours = async (workingHoursData: WorkingHoursData): Promise<ApiResponse<WeeklyHoursData>> => {
  try {
    // First, update weekly hours
    const weeklyUrl = `${endpoints.workingHours}`;
    const weeklyData = transformWeeklyHoursForBackend(workingHoursData.weeklyHours);

    // Make sure all time slots have duration
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    days.forEach(day => {
      if (Array.isArray(weeklyData[day as keyof typeof weeklyData])) {
        const slots = weeklyData[day as keyof typeof weeklyData];
        slots.forEach((slot: TimeSlot) => {
          if (!slot.duration) {
            // Calculate duration if missing
            const startParts = slot.startTime.split(':').map(Number);
            const endParts = slot.endTime.split(':').map(Number);
            const startMinutes = startParts[0] * 60 + startParts[1];
            const endMinutes = endParts[0] * 60 + endParts[1];
            slot.duration = endMinutes > startMinutes ? endMinutes - startMinutes : (endMinutes + 24 * 60) - startMinutes;
          }
        });
      }
    });

    const weeklyResponse = await axiosInstance.patch<ApiResponse<WeeklyHoursData>>(weeklyUrl, weeklyData);

    // Then, handle specific days
    // Note: We always use POST for specific days, even when updating, as the backend API
    // is designed to handle both creation and updates with POST requests for specific days
    // Only proceed if there are actually specific days to save
    if (workingHoursData.specificDays && workingHoursData.specificDays.length > 0) {
      try {
        await saveSpecificWorkingHours(workingHoursData.specificDays);
      } catch (specificError) {
        console.error("Error updating specific days:", specificError);
        // Continue even if specific days fail
      }
    }

    return weeklyResponse.data;
  } catch (err) {
    console.error("Error updating working hours:", err);

    // Log more detailed error information
    if (err && typeof err === 'object' && 'response' in err) {
      const axiosError = err as AxiosError;
      if (axiosError.response) {
        console.error("Error response data:", axiosError.response.data);
        console.error("Error response status:", axiosError.response.status);
      }
    }

    throw err;
  }
};

// Fetch working hours data (GET method)
export const fetchWorkingHours = async (): Promise<{ data: WorkingHoursData, hasExistingData: boolean }> => {
  try {
    // Fetch weekly hours
    const weeklyUrl = `${endpoints.workingHours}`;
    const weeklyResponse = await axiosInstance.get<ApiResponse<WeeklyHoursData>>(weeklyUrl);
    const weeklyData = weeklyResponse.data.data || {};

    // Check if therapist ID exists in the data
    const hasExistingData = weeklyData && weeklyData._id ? true : false;

    // Fetch specific days
    const specificDays = await fetchSpecificWorkingHours();

    // Combine the data
    const combinedData = {
      ...weeklyData,
      specificDays
    };

    // Transform the combined data to match the frontend schema
    const transformedData = transformDataForFrontend(combinedData);

    return {
      data: transformedData,
      hasExistingData
    };
  } catch (err) {
    // Log error for debugging
    console.debug("Error fetching working hours:", err);
    // Return default working hours if fetch fails
    return {
      data: getDefaultWorkingHours(),
      hasExistingData: false
    };
  }
};
