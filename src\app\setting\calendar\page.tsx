"use client";
import Button from "@/components/common/Button";
import Tabs from "@/components/common/Tabs";
import SettingLayout from "@/layout/dashboard/SettingLayout";
import {
  createMultipleClients,
  eventCalendarSync,
  getCalendarEventClients,
  updateSyncStatus,
  useGetCalendarEvents,
  useGetEnhancedCalendarEvents,
} from "@/services/setting.service";
import { formatDate, formatTime } from "@/utils/axios";
import { Clock, EnvelopeSimple, Repeat } from "@phosphor-icons/react";
import moment from "moment";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import toast from "react-hot-toast";
import Modal from "@/components/common/Modal";
import THeader from "@/components/dashboard/common/table/THeader";
import CalendarTBody from "@/components/dashboard/common/table/CalendarTBody";

import ConflictModal from "@/components/common/ConflictModal";
import WebhookManagement from "@/components/dashboard/WebhookManagement";
import ManualSyncButton from "@/components/dashboard/ManualSyncButton";

import { AxiosError } from "axios";

const CalendarTab = [
  { label: "New Events", value: "New Events" },
  { label: "Rescheduled Events", value: "Rescheduled Events" },
  { label: "Non Syncable Events", value: "Non Syncable Events" },
];

const CalendarTableHeader = [
  "S.no",
  "Session",
  "Email",
  "Client Name ",
  "Mobile Number",
  "Amount",
];

interface emails {
  email?: string;
  organizer?: boolean;
  self?: boolean;
}

interface Item {
  kind?: string;
  summary?: string;
  start?: {
    dateTime: string;
    date: string;
    timeZone: string;
  };
  originalStartTime: {
    dateTime: string;
  };
  creator: {
    email: string;
  };
  email: string;
  description: string;
  status: string;
  depression: string;
  eventIds?: string[] | undefined;
  id?: string;
  totalRecurrenceData?: number;
  firstRecurrenceData?: {
    status?: string;
    fromDate?: string;
  };
  attendees?: emails[];
  // Additional properties for grouping
  recurringEventId?: string;
  iCalUID?: string;
  eventOccurences?: number;
  rescheduleInfo?: {
    originalStartTime?: string;
    originalEndTime?: string;
    newStartTime?: string;
    newEndTime?: string;
  };
}

interface TableRow {
  isPresent: boolean;
  session: string;
  email: string;
  clientName: string;
  mobile: number;
  amount: number;
}

interface Attendee {
  email: string;
  self: boolean;
}

interface Event {
  id: string;
  summary: string;
  attendees?: Attendee[];
}

interface EventData {
  calendarEventId: string;
  exists: boolean;
  summary: string;
  attendee: string;
  client?: {
    name: string;
    mobileNumber: string;
    Amount: number;
  };
}

const CalendarSetting = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(CalendarTab[0]);
  const [loading, setLoading] = useState(false);
  const [selectedEventIds, setSelectedEventIds] = useState<string[]>([]);
  const [tableData, setTableData] = useState<TableRow[]>([]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [updateDisabled, setUpdateDisabled] = useState(false);
  const [syncDisabled, setSyncDisabled] = useState(true);
  const [isUpdateDisabled, setIsUpdateDisabled] = useState(false);
  // Use enhanced calendar data as primary source
  const { enhancedCalendarData, enhancedCalendarLoading } = useGetEnhancedCalendarEvents();

  // Fallback to regular data if enhanced is not available
  const { resyncCalendarData, resyncCalendarLoading } = useGetCalendarEvents();

  // Use enhanced data if available, otherwise fall back to regular data
  const calendarData = enhancedCalendarData || resyncCalendarData;
  const calendarLoading = enhancedCalendarLoading || resyncCalendarLoading;

  // Group events by recurring series
  const groupEventsBySeries = (events: Item[]) => {
    const grouped: { [key: string]: { event: Item; count: number; eventIds: string[] } } = {};

    events?.forEach((event) => {
      const seriesKey = event.recurringEventId || event.iCalUID || event.id || `single-${Math.random()}`;

      if (!seriesKey) return; // Skip if no key available

      if (grouped[seriesKey]) {
        // Add to existing group
        grouped[seriesKey].count += 1;
        if (event.id) {
          grouped[seriesKey].eventIds.push(event.id);
        }
      } else {
        // Create new group
        grouped[seriesKey] = {
          event: { ...event },
          count: 1,
          eventIds: event.id ? [event.id] : []
        };
      }
    });

    // Convert back to Item format with correct counts
    return Object.values(grouped).map(group => ({
      ...group.event,
      eventOccurences: group.count,
      eventIds: group.eventIds
    }));
  };

  // Debug logging
  React.useEffect(() => {
    if (calendarData) {
      // Group rescheduled events for debugging purposes
      groupEventsBySeries(calendarData.rescheduled_events || []);
    }
  }, [calendarData]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConflictModalOpen, setIsConflictModalOpen] = useState(false);
  const [conflictData, setConflictData] = useState<{
    message: string;
    conflicts: { startTime: string; endTime: string }[];
  }>({
    message: "",
    conflicts: [],
  });

  const handleContinueClick = async () => {
    setUpdateDisabled(false);
    setSyncDisabled(true);

    try {
      const selectedEvents = calendarData?.syncable?.filter(
        (event: Event) => selectedEventIds.includes(event.id)
      ) || [];

      if (!selectedEvents?.length) {
        console.warn("No selected events found.");
        return;
      }

      const result = await getCalendarEventClients(selectedEvents);

      setIsModalOpen(true);

      // Prepare table data with only selected events
      const newTableData = result.events
        .filter((event: EventData) =>
          selectedEventIds.includes(event.calendarEventId)
        )
        .map((event: EventData) => ({
          isPresent: event.exists,
          session: event.summary,
          email: event.attendee,
          clientName: event.exists && event.client ? event.client.name : "",
          mobile:
            event.exists && event.client
              ? Number(event.client.mobileNumber) || ""
              : "",
          amount:
            event.exists && event.client
              ? Number(event.client.Amount) || ""
              : "",
        }));

      setTableData(newTableData);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  // Validate form before updating
  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    tableData.forEach((row, index) => {
      if (!row.clientName.trim()) {
        newErrors[`${index}-clientName`] = "Name is required*";
      }
      if (!row.amount) {
        newErrors[`${index}-amount`] = "Amount is required*";
      }

      const mobileStr = row.mobile?.toString().trim();
      if (mobileStr && !/^\d{10}$/.test(mobileStr)) {
        newErrors[`${index}-mobile`] =
          "Mobile number must be exactly 10 digits*";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdate = async () => {
    if (!validateForm()) return;

    try {
      const clients = tableData.map((row) => ({
        name: row.clientName,
        defaultSessionAmount: row.amount.toString(),
        email: row.email,
        phone: row.mobile.toString(),
      }));

      await createMultipleClients(clients);

      setUpdateDisabled(true);
      setIsUpdateDisabled(true);
    } catch (error) {
      console.error("Update failed", error);
    }
  };

  const handleRemoveSelectedEvents = () => {
    setSelectedEventIds([]);
  };

  const handleChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    item: Item
  ) => {
    if (event.target.checked) {
      let newEventIds = [];
      if (item?.eventIds && item?.eventIds.length > 0) {
        newEventIds = [...item?.eventIds];
      } else {
        newEventIds.push(item.id);
      }

      setSelectedEventIds([
        ...selectedEventIds,
        ...(newEventIds.filter((id) => id !== undefined) as string[]),
      ]);
      setSelectedEventIds([
        ...selectedEventIds,
        ...(newEventIds.filter((id) => id !== undefined) as string[]),
      ]);
    } else {
      let nIds = [];
      if (item?.eventIds && item?.eventIds.length > 0) {
        nIds = selectedEventIds.filter(
          (selectedId) => !item?.eventIds?.includes(selectedId)
        );
      } else {
        nIds = selectedEventIds.filter((selectedId) => selectedId !== item?.id);
      }
      setSelectedEventIds(nIds);
    }
  };

  async function updateSyncEventStatus() {
    await updateSyncStatus().catch((err) => {
      console.log(err);
    });
  }

  const syncCalendarEvents = async () => {
    setLoading(true);

    if (selectedEventIds.length <= 0) {
      toast.error("Please select at least one session");
      setLoading(false);
      return false;
    }

    try {
      const res = await eventCalendarSync({ eventIds: selectedEventIds });

      if (res.status === 200) {
        // Check if there are conflicts in the response
        if (res.data && res.data.success === false && res.data.conflicts) {
          // Handle conflicts
          setConflictData({
            message: res.data.message || "Conflicts found with existing sessions.",
            conflicts: res.data.conflicts || []
          });
          setTimeout(() => {
            setIsConflictModalOpen(true);
          }, 100);
          return false; // Return false to indicate conflicts
        }

        // No conflicts, sync was successful
        return true; // Return true to indicate success
      }
    } catch (err: unknown) {
      const axiosError = err as AxiosError;
      console.error(axiosError);

      // Check if the error response contains conflict data
      if (axiosError.response && axiosError.response.data &&
          typeof axiosError.response.data === 'object' &&
          'success' in axiosError.response.data &&
          axiosError.response.data.success === false &&
          'conflicts' in axiosError.response.data) {
        const errorData = axiosError.response.data as {
          message?: string;
          conflicts?: Array<{ startTime: string; endTime: string }>
        };
        setConflictData({
          message: errorData.message || "Conflicts found with existing sessions.",
          conflicts: errorData.conflicts || []
        });
        setTimeout(() => {
          setIsConflictModalOpen(true);
        }, 100);
        return false; // Return false to indicate conflicts
      } else {
        toast.error("Sync failed. Please try again.");
      }
    } finally {
      setLoading(false);
    }

    return false; // Return false by default for error cases
  };

  const handleSync = async () => {
    try {
      if (selectedEventIds.length <= 0) {
        toast.error("Please select at least one session", {
          position: "top-center",
        });
        return;
      }

      setLoading(true);

      // Proceed with sync directly without confirmation modal
      const syncResult = await syncCalendarEvents();

      // Only close modal if sync was successful
      if (syncResult) {
        setIsModalOpen(false);
        toast.success("Calendar events synced successfully");
        await updateSyncEventStatus();
        router.push("/session");
      }
    } catch (error) {
      console.error("Sync failed", error);
      toast.error("Sync failed. Please try again.");
    } finally {
      setLoading(false);
      handleRemoveSelectedEvents();
      setIsModalOpen(false);
    }
  };





  return (
    <SettingLayout>
      <div>
        <h1 className="text-xl_30 font-semibold text-primary">
          Calendar Setting
        </h1>

        {/* Webhook Management Panel */}
        <div className="pt-6">
          <WebhookManagement therapistId="current-therapist-id" />
        </div>

        <div className="pt-6">
          <div className="flex justify-between items-center mb-4">
            <Tabs
              tabs={CalendarTab}
              activeTab={activeTab?.label}
              setActiveTab={setActiveTab}
              handleRemoveSelectedEvents={handleRemoveSelectedEvents}
              sessionCount={
                activeTab?.label === "New Events"
                  ? calendarData?.syncable?.length || 0
                  : activeTab?.label === "Rescheduled Events"
                  ? groupEventsBySeries(calendarData?.rescheduled_events || []).length || 0
                  : calendarData?.not_syncable?.length || 0
              }
            />
            {activeTab?.label === "Rescheduled Events" && (
              <div className="flex items-center space-x-3">
                <ManualSyncButton
                  size="sm"
                  onSyncComplete={() => {
                    // Refresh calendar data after sync
                    window.location.reload();
                  }}
                />
              </div>
            )}
          </div>
          <div className="flex justify-between">
            {activeTab?.label === "New Events" &&
              calendarData?.syncable?.length > 0 && (
                <div className="flex items-center justify-between p-5">
                  <label htmlFor="syncAll" className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="syncAll"
                      className="w-4 h-4 accent-green-600 cursor-pointer"
                      onChange={(e) => {
                        if (e.target.checked) {
                          // Select all new event IDs
                          const allEventIds = calendarData?.syncable
                            ?.map((event: Item) => event.id)
                            .filter(
                              (id: number) => id !== undefined
                            ) as string[];
                          setSelectedEventIds(allEventIds);
                        } else {
                          // Deselect all
                          setSelectedEventIds([]);
                        }
                      }}
                    />

                    <p className="text-base/6 text-green-600 font-semibold capitalize">
                      Sync All New Events
                    </p>
                  </label>
                </div>
              )}

          </div>
          {calendarLoading ? ( // Check if loading
            <div className="grid sm:grid-cols-2 gap-4.5 py-7.5">
              {/* Display skeletons for loading state */}
              {Array.from({ length: 4 }).map((_, index) => (
                <div
                  key={index}
                  className="border border-green-600/20 rounded-base overflow-hidden animate-pulse"
                >
                  <div className="flex items-center justify-between p-5 bg-yellow-100">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
                      <div className="h-6 w-1/3 bg-gray-200 rounded-full"></div>
                    </div>
                    <div className="h-6 w-1/4 bg-gray-200 rounded-full"></div>
                  </div>
                  <div className="p-5">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <div className="h-4 w-1/2 bg-gray-200 rounded-full"></div>
                        <div className="h-4 w-2/3 bg-gray-200 rounded-full"></div>
                      </div>
                      <div className="relative group">
                        <div className="h-4 w-1/4 bg-gray-200 rounded-full"></div>
                      </div>
                    </div>
                    <hr className="my-5" />
                    <div className="h-4 w-1/4 bg-gray-200 rounded-full"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <>
              {activeTab?.label === "New Events" && (
                <div>
                  <div className="grid sm:grid-cols-2 gap-4.5 py-7.5">
                    {calendarData?.syncable?.length > 0 &&
                      calendarData?.syncable?.map(
                        (item: Item, index: number) => {
                          const attendees =
                            item.attendees &&
                            item.attendees.filter(
                              (attend) => attend.self != true
                            );
                          return (
                            <div
                              key={index}
                              className="border border-green-600/20 rounded-base overflow-hidden"
                            >
                              <div className="flex items-center justify-between p-5 bg-yellow-100">
                                <label className="flex items-center gap-3">
                                  <input
                                    type="checkbox"
                                    className="w-4 h-4 accent-green-600 cursor-pointer"
                                    checked={
                                      item.id
                                        ? selectedEventIds.includes(item.id)
                                        : false
                                    }
                                    onChange={(event) =>
                                      handleChange(event, item)
                                    }
                                  />
                                  <p className="text-base/6 text-green-600 font-semibold capitalize">
                                    {item?.summary}
                                  </p>
                                </label>
                                <p className="text-base/6 text-green-600/70 font-semibold">
                                  Session :{" "}
                                  {index > 9 ? index + 1 : `0${index + 1}`}
                                </p>
                              </div>
                              <div className="p-5">
                                <div className="flex justify-between items-start">
                                  <div className="space-y-2">
                                    <p className="flex items-center gap-2 text-base/6 text-primary/90 font-semibold">
                                      <Clock
                                        size={24}
                                        className="text-[#44B7E5]"
                                      />{" "}
                                      {item.start?.timeZone ===
                                      "Asia/Kolkata" ? (
                                        <>
                                          {item.start?.dateTime ? (
                                            <>
                                              {moment(
                                                item.start?.dateTime
                                              ).format("YYYY-MM-DD")}{" "}
                                              | {/* Only Date */}
                                              {moment(
                                                item.start?.dateTime
                                              ).format("hh:mm A")}{" "}
                                              {/* Only Time (HH:MM) */}
                                            </>
                                          ) : (
                                            <>
                                              {moment(item.start?.date).format(
                                                "hh:mm"
                                              )}{" "}
                                              {/* Only Time (HH:MM) */}
                                            </>
                                          )}
                                        </>
                                      ) : (
                                        <>
                                          {item.start?.dateTime ? (
                                            <>
                                              {formatDate(
                                                item.start?.dateTime || ""
                                              )}{" "}
                                              |{" "}
                                              {formatTime(
                                                item.start?.dateTime || ""
                                              )}
                                            </>
                                          ) : (
                                            <>
                                              {formatDate(
                                                item.start?.date || ""
                                              )}
                                            </>
                                          )}
                                        </>
                                      )}
                                    </p>
                                    <p className="flex items-center gap-2 text-base/6 text-primary/90">
                                      <EnvelopeSimple
                                        size={24}
                                        className="text-[#F6A002]"
                                      />
                                      {attendees &&
                                        attendees.length > 0 &&
                                        attendees[0].email}
                                    </p>
                                  </div>
                                  <div className="relative group">
                                    <p className="text-sm/5 text-green-500 px-3 py-1.5 rounded-full inline-block bg-green-200 capitalize">
                                      {item?.status} !
                                    </p>
                                  </div>
                                </div>
                                <hr className="my-5" />
                                <p className="text-base/6 text-primary/90 font-medium flex items-center gap-2">
                                  <Repeat size={22} /> Frequency: +
                                  {item?.eventIds?.length}
                                </p>
                              </div>
                            </div>
                          );
                        }
                      )}
                  </div>
                  {calendarData?.syncable?.length === 0 && (
                    <div className="p-15px text-center text-primary/70 text-sm/5">
                      <div className="flex flex-col items-center justify-center pt-5">
                        <Image
                          width={1000}
                          height={1000}
                          src="/assets/images/dashboard/not-found-session-list.webp"
                          alt="no-data"
                          className="w-[185px] h-auto"
                        />
                        <p className="text-xl/6 font-bold pt-4 text-primary">
                          No Events Found!
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}
              {activeTab?.label === "Rescheduled Events" && (
                <div>
                  {/* Orange note for automatic detection */}
                  <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <svg className="w-5 h-5 text-orange-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm text-orange-800">
                          <span className="font-medium">Automatic Detection:</span> We will detect changes automatically in 5 minutes. If the changes are still not reflected, you can use the manual sync button above.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="grid sm:grid-cols-2 gap-4.5 py-7.5">
                    {(() => {
                      const groupedRescheduled = groupEventsBySeries(calendarData?.rescheduled_events || []);
                      return groupedRescheduled.map(
                        (item: Item, index: number) => {
                          const attendees =
                            item.attendees &&
                            item.attendees.filter(
                              (attend) => attend.self != true
                            );
                          return (
                            <div
                              key={index}
                              className="border border-green-600/20 rounded-base overflow-hidden"
                            >
                              <div className="flex items-center justify-between p-5 bg-yellow-100">
                                <label className="flex items-center gap-3">
                                  <p className="text-base/6 text-green-600 font-semibold capitalize">
                                    {item?.summary}
                                  </p>
                                </label>
                                <p className="text-base/6 text-green-600/70 font-semibold">
                                  Session :{" "}
                                  {index > 9 ? index + 1 : `0${index + 1}`}
                                </p>
                              </div>
                              <div className="p-5">
                                <div className="flex justify-between items-start">
                                  <div className="space-y-2">
                                    <p className="flex items-center gap-2 text-base/6 text-primary/90 font-semibold">
                                      <Clock
                                        size={24}
                                        className="text-[#44B7E5]"
                                      />{" "}
                                      {item.start?.timeZone ===
                                      "Asia/Kolkata" ? (
                                        <>
                                          {item.start?.dateTime ? (
                                            <>
                                              {moment(
                                                item.start?.dateTime
                                              ).format("YYYY-MM-DD")}{" "}
                                              | {/* Only Date */}
                                              {moment(
                                                item.start?.dateTime
                                              ).format("hh:mm A")}{" "}
                                              {/* Only Time (HH:MM) */}
                                            </>
                                          ) : (
                                            <>
                                              {moment(item.start?.date).format(
                                                "hh:mm"
                                              )}{" "}
                                              {/* Only Time (HH:MM) */}
                                            </>
                                          )}
                                        </>
                                      ) : (
                                        <>
                                          {item.start?.dateTime ? (
                                            <>
                                              {formatDate(
                                                item.start?.dateTime || ""
                                              )}{" "}
                                              |{" "}
                                              {formatTime(
                                                item.start?.dateTime || ""
                                              )}
                                            </>
                                          ) : (
                                            <>
                                              {formatDate(
                                                item.start?.date || ""
                                              )}
                                            </>
                                          )}
                                        </>
                                      )}
                                    </p>
                                    <p className="flex items-center gap-2 text-base/6 text-primary/90">
                                      <EnvelopeSimple
                                        size={24}
                                        className="text-[#F6A002]"
                                      />
                                      {attendees &&
                                        attendees.length > 0 &&
                                        attendees[0].email}
                                    </p>
                                  </div>
                                  <div className="relative group">
                                    <p className="text-sm/5 text-green-500 px-3 py-1.5 rounded-full inline-block bg-green-200 capitalize">
                                      Rescheduled !
                                    </p>
                                  </div>
                                </div>
                                <hr className="my-5" />
                                <p className="text-base/6 text-primary/90 font-medium flex items-center gap-2">
                                  <Repeat size={22} /> Frequency: +
                                  {item?.eventOccurences || item?.eventIds?.length || 1}
                                </p>
                              </div>
                            </div>
                          );
                        }
                      );
                    })()}
                  </div>
                  {groupEventsBySeries(calendarData?.rescheduled_events || []).length === 0 && (
                    <div className="p-15px text-center text-primary/70 text-sm/5">
                      <div className="flex flex-col items-center justify-center pt-5">
                        <Image
                          width={1000}
                          height={1000}
                          src="/assets/images/dashboard/not-found-session-list.webp"
                          alt="no-data"
                          className="w-[185px] h-auto"
                        />
                        <p className="text-xl/6 font-bold pt-4 text-primary">
                          No Rescheduled Events Found!
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}
              {activeTab?.label === "Non Syncable Events" && (
                <div>
                  <div className="grid sm:grid-cols-2 gap-4.5 py-7.5">
                    {calendarData?.not_syncable?.map(
                      (item: Item, index: number) => {
                        const attendees =
                          item.attendees &&
                          item.attendees.filter(
                            (attend) => attend.self != true
                          );
                        return (
                          <div
                            key={index}
                            className="border border-green-600/20 rounded-base overflow-hidden "
                          >
                            <div className="flex items-center justify-between p-5 bg-yellow-100 ">
                              <label className="flex items-center gap-3">
                                {/* <input
                                type="checkbox"
                                className="w-4 h-4 accent-green-600 cursor-pointer"
                              /> */}
                                <p className="text-base/6 text-green-600 font-semibold capitalize">
                                  {item?.summary}
                                </p>
                              </label>
                              <p className="text-base/6 text-green-600/70 font-semibold">
                                Session :{" "}
                                {index > 9 ? index + 1 : `0${index + 1}`}
                              </p>
                            </div>
                            <div className="p-5">
                              <div className="flex justify-between items-start">
                                <div className="space-y-2">
                                  <p className="flex items-center gap-2 text-base/6 text-primary/90 font-semibold">
                                    <Clock
                                      size={24}
                                      className="text-[#44B7E5]"
                                    />{" "}
                                    {item.start?.timeZone === "Asia/Kolkata" ? (
                                      <>
                                        {item.start?.dateTime ? (
                                          <>
                                            {moment(
                                              item.start?.dateTime
                                            ).format("YYYY-MM-DD")}{" "}
                                            | {/* Only Date */}
                                            {moment(
                                              item.start?.dateTime
                                            ).format("hh:mm A")}{" "}
                                            {/* Only Time (HH:MM) */}
                                          </>
                                        ) : (
                                          <>
                                            {moment(item.start?.date).format(
                                              "hh:mm"
                                            )}{" "}
                                            {/* Only Time (HH:MM) */}
                                          </>
                                        )}
                                      </>
                                    ) : (
                                      <>
                                        {item.start?.dateTime ? (
                                          <>
                                            {formatDate(
                                              item.start?.dateTime || ""
                                            )}{" "}
                                            |{" "}
                                            {formatTime(
                                              item.start?.dateTime || ""
                                            )}
                                          </>
                                        ) : (
                                          <>
                                            {formatDate(item.start?.date || "")}
                                          </>
                                        )}
                                      </>
                                    )}
                                  </p>
                                  {attendees &&
                                    attendees.length > 0 &&
                                    attendees[0].email && (
                                      <p className="flex items-center gap-2 text-base/6 text-primary/90">
                                        <EnvelopeSimple
                                          size={24}
                                          className="text-[#F6A002]"
                                        />
                                        {attendees &&
                                          attendees.length > 0 &&
                                          attendees[0].email}
                                      </p>
                                    )}
                                </div>
                                <div className="relative group">
                                  <p className="text-sm/5 text-green-500 px-3 py-1.5 rounded-full inline-block bg-green-200">
                                    {item?.status} !
                                  </p>
                                  <div className="absolute right-0 top-4 p-2.5 bg-white w-[202px] rounded-base shadow-[0px_4px_20.9px_0px_#0000001A] invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-all duration-300">
                                    <p className="text-xs_18 text-primary">
                                      This events does not have any{" "}
                                      <span className="font-semibold">
                                        attendees email
                                      </span>
                                      , hence this can not be synced!
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <hr className="my-5" />
                              <p className="text-base/6 text-primary/90 font-medium flex items-center gap-2">
                                <Repeat size={22} /> Frequency: +
                                {item?.eventIds?.length}
                              </p>
                            </div>
                          </div>
                        );
                      }
                    )}
                  </div>
                  {calendarData?.not_syncable?.length === 0 && (
                    <div className="p-15px text-center text-primary/70 text-sm/5">
                      <div className="flex flex-col items-center justify-center pt-5">
                        <Image
                          width={1000}
                          height={1000}
                          src="/assets/images/dashboard/not-found-session-list.webp"
                          alt="no-data"
                          className="w-[185px] h-auto"
                        />
                        <p className="text-xl/6 font-bold pt-4 text-primary">
                          No Events Found!
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
        {activeTab?.value === "New Events" &&
          calendarData?.syncable?.length > 0 &&
          selectedEventIds?.length > 0 && (
            <div className="sticky bottom-0 z-40 bg-white py-2.5 flex justify-end items-center">
              <Button
                variant="filled"
                className="mt-auto"
                // onClick={syncCalendarEvents}
                onClick={handleContinueClick}
                disabled={loading}
              >
                {loading ? "Loading..." : "Continue"}
              </Button>
            </div>
          )}

        {isModalOpen && (
          <Modal
            open={isModalOpen}
            handler={() => setIsModalOpen(false)}
            size="xl"
            email={calendarData?.syncable?.[0]?.attendees?.[0]?.email}
          >
            <div className="p-5">
              <h2 className="text-xl font-semibold">Required Details*</h2>
              <div className="overflow-x-auto py-4">
                <div className="rounded-2xl overflow-hidden border border-gray-300">
                  <table className="w-full bg-white border-separate">
                    <THeader data={CalendarTableHeader} />
                    <CalendarTBody
                      TableData={{ data: tableData }}
                      handleUpdate={handleUpdate}
                      handleSync={handleSync}
                      errors={errors}
                      setErrors={setErrors}
                      setTableData={setTableData}
                      isUpdateDisabled={isUpdateDisabled}
                    />
                  </table>
                </div>
              </div>

              <div className="flex justify-center gap-3 mt-4">
              {tableData.every((row) => row.clientName && row.amount) && isUpdateDisabled ? (
                  <Button
                    onClick={handleSync}
                    className="px-4 py-2 text-white rounded"
                    disabled={loading && syncDisabled}
                  >
                    {loading ? "Syncing..." : "Sync"}
                  </Button>
                ) : (
                  <Button
                    onClick={handleUpdate}
                    className="px-4 py-2 text-white rounded"
                    disabled={updateDisabled}
                  >
                    Update
                  </Button>
                )}
              </div>
            </div>
          </Modal>
        )}



        {/* Conflict Modal */}
        <ConflictModal
          open={isConflictModalOpen}
          onClose={() => {
            setIsConflictModalOpen(false);
            // Clear conflict data when closing
            setConflictData({
              message: "",
              conflicts: []
            });
          }}
          message={conflictData.message}
          conflicts={conflictData.conflicts}
        />
      </div>
    </SettingLayout>
  );
};

export default CalendarSetting;