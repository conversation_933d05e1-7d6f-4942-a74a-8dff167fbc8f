import axiosInstance from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import axios from "axios";

// Define the public calendar profile data interface
export interface PublicCalendarProfileData {
  name: string;
  pronouns: string;
  yearsOfExperience: string;
  gender: string;
  ageLimit: {
    min: string;
    max: string;
  };
  designation: string;
  therapyTypes: string[];
  fee: string;
  location: string;
  slotTypes: string[];
  timeZone: string;
  qualifications: string;
  values: string[];
  concerns: string[];
  practiceDescription: string;
  profilePhoto?: string;
  languages: string[];
}

// Define the onboarding data interface for API request
export interface TherapistOnboardingData {
  name?: string;
  pronouns?: string;
  yearsOfExperience?: number;
  gender?: string;
  minAge?: number;
  maxAge?: number;
  designation?: string;
  therapyTypes?: string[];
  languages?: string[];
  minFee?: number;
  maxFee?: number;
  location?: string[];
  slotType?: string[];
  timeZone?: string;
  professionalQualification?: string;
  values?: string[];
  concerns?: string[];
  practiceApproach?: string;
}

// Define API response interface
export interface ApiResponse<T = unknown> {
  status: string;
  message: string;
  data: T;
  responseCode: number;
}

export const submitTherapistOnboarding = async (
  profilePhoto: File | null,
  onboardingData: TherapistOnboardingData
): Promise<ApiResponse<unknown>> => {
  try {
    // Create FormData object
    const formData = new FormData();

    // Add profile photo if available
    if (profilePhoto) {
      formData.append('file', profilePhoto);
    }

    // Ensure languages is an array before stringifying
    if (onboardingData.languages === undefined || onboardingData.languages === null) {
      onboardingData.languages = [];
    } else if (!Array.isArray(onboardingData.languages)) {
      onboardingData.languages = [];
    }

    // Add onboarding data as JSON string
    const onboardingDataString = JSON.stringify(onboardingData);
    formData.append('onboardingData', onboardingDataString);
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    const response = await axiosInstance.post(
      endpoints.publicCalendar,
      formData,
      config
    );

    return response.data;
  } catch (error) {
    console.error('Error submitting therapist onboarding data:', error);
    throw error;
  }
};

// Define therapist data interface
export interface TherapistData {
  // Basic information
  fromPublicCalender?: boolean;
  _id?: string;
  identifier?: string;
  name?: string | Record<string, unknown>;
  email?: string;
  phone?: string;

  // Profile details
  profilePhoto?: string;
  profilePicUrl?: string | Record<string, unknown>;
  pronouns?: string | Record<string, unknown>;
  gender?: string | Record<string, unknown>;
  yearsOfExperience?: number | string;
  designation?: string | Record<string, unknown>;

  // Professional information
  languages?: string[];
  concerns?: string[];
  professionalQualification?: string | Record<string, unknown>;
  qualifications?: string[];
  therapyTypes?: string[];
  slotType?: string[];
  values?: string[];

  // Practice details
  minFee?: number | string;
  maxFee?: number | string;
  minAge?: number;
  maxAge?: number;
  location?: string[];
  timeZone?: string | Record<string, unknown>;
  practiceApproach?: string | Record<string, unknown>;
  bookingMessage?: string;
  // Use unknown instead of any for better type safety
  [key: string]: unknown;
}

export const updateTherapistProfile = async (
  onboardingData: TherapistOnboardingData,
  profilePhoto?: File | null,
  therapistData?: TherapistData,
  forceMethod?: 'POST' | 'PATCH'
): Promise<ApiResponse<unknown>> => {
  try {
    const formData = new FormData();

    // Add profile photo if available
    if (profilePhoto) {
      formData.append('file', profilePhoto);
    }

    // Ensure languages is an array before stringifying
    if (onboardingData.languages === undefined || onboardingData.languages === null) {
      onboardingData.languages = [];
    } else if (!Array.isArray(onboardingData.languages)) {
      onboardingData.languages = [];
    }

    // Add onboarding data as JSON string
    const onboardingDataString = JSON.stringify(onboardingData);
    formData.append('onboardingData', onboardingDataString);


    let response;

    const currentTherapistData = therapistData || (await fetchTherapistProfile()).data?.[0];

    // Determine which HTTP method to use
    let method: 'POST' | 'PATCH';

    if (forceMethod) {
      method = forceMethod;
    } else if (currentTherapistData?.fromPublicCalender === false) {
      method = 'POST';
    } else {
      method = 'PATCH';
    }

    // Make the API call with the determined method
    if (method === 'POST') {
      response = await axiosInstance.post(
        endpoints.publicCalendar,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );


    } else {
      response = await axiosInstance.patch(
        endpoints.publicCalendar,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
    }

    return response.data;
  } catch (error) {
    console.error('Error updating therapist profile data:', error);

    // Log more detailed error information
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as {
        response?: {
          data: unknown;
          status: number;
          headers: unknown
        };
        request?: unknown;
        message?: string;
      };

      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      if (axiosError.response) {
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
        console.error('Error response headers:', axiosError.response.headers);
      } else if ('request' in error) {
        // The request was made but no response was received
        console.error('Error request:', axiosError.request);
      } else if ('message' in error) {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', axiosError.message);
      }
    }

    throw error;
  }
};

export interface TherapistProfileResponse {
  status: string;
  data: TherapistData[];
  message: string;
  responseCode: number;
}

export const fetchTherapistProfile = async (): Promise<TherapistProfileResponse> => {
  try {
    const response = await axiosInstance.get(endpoints.publicCalendar);

    // Check if languages data exists and log it
    if (response.data && response.data.status === "success" && Array.isArray(response.data.data) && response.data.data.length > 0) {
      const profileData = response.data.data[0];

      if (profileData.languages === undefined || profileData.languages === null) {
        console.warn("Languages is undefined or null in API response");
        profileData.languages = [];
      } else if (!Array.isArray(profileData.languages)) {
        console.warn("Languages is not an array in API response:", typeof profileData.languages, profileData.languages);
        try {
          if (typeof profileData.languages === 'string') {
            profileData.languages = [profileData.languages];
          } else {
            profileData.languages = [];
          }
        } catch (e) {
          console.error('Error converting languages to array:', e);
          profileData.languages = [];
        }
      } else if (profileData.languages.length === 0) {
        console.warn("Languages array is empty in API response");
      }
    }

    return response.data;
  } catch (error) {
    console.error('Error fetching therapist profile data:', error);
    throw error;
  }
};

// Fetch therapist profile by ID
export const fetchTherapistProfileById = async (therapistId: string): Promise<TherapistProfileResponse> => {
  try {
    const response = await axiosInstance.get(`${endpoints.publicCalendar}?therapistId=${therapistId}`);

    return response.data;
  } catch (error) {
    console.error('Error fetching therapist profile by ID:', error);
    throw error;
  }
};

export const getTherapistById = async (id: string): Promise<TherapistProfileResponse> => {
  try {
    const response = await axiosInstance.get(`${endpoints.getTherapistById}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching therapist by ID:', error);
    throw error;
  }
};

/**
 * Public version of getTherapistById that doesn't require authentication
 * This uses the public endpoint that has no middleware or validation
 */
export const getPublicTherapistById = async (identifier: string): Promise<TherapistProfileResponse> => {
  try {
    const directAxios = axios.create({
      baseURL: process.env.BASE_API_URL as string,
      headers: {
        'Content-Type': 'application/json',
        "ngrok-skip-browser-warning": "3456"
      }
    });

    // Make a direct call to the backend API
    const response = await directAxios.get(`/therapist/get/${identifier}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching public therapist by identifier:', error);

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as {
        response?: {
          data: unknown;
          status: number;
        };
      };

      if (axiosError.response) {
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
      }
    }
    throw error;
  }
};

// Define working hours response interface
export interface WorkingHoursSlot {
  startTime: string;
  endTime: string;
  duration: number;
  _id: string;
}

export interface SpecificWorkingHours {
  _id: string;
  therapistId: string;
  date: string;
  slots: WorkingHoursSlot[];
}

export interface RegularWorkingHours {
  _id: string;
  therapistId: string;
  sunday: WorkingHoursSlot[];
  monday: WorkingHoursSlot[];
  tuesday: WorkingHoursSlot[];
  wednesday: WorkingHoursSlot[];
  thursday: WorkingHoursSlot[];
  friday: WorkingHoursSlot[];
  saturday: WorkingHoursSlot[];
}

// Define conflict interface for booked slots
export interface ConflictSchedule {
  type: 'regular' | 'specific';
  day?: string; // For regular conflicts
  date?: string; // For specific conflicts
  conflictingSlot: {
    startTime: string;
    endTime: string;
    duration: number;
  };
  existingSchedule: {
    fromDate: string;
    toDate: string;
  };
}

export interface WorkingHoursResponse {
  responseCode: number;
  status: string;
  message: string;
  data: {
    regularWorkingHours: RegularWorkingHours[];
    specificWorkingHours: SpecificWorkingHours[];
    conflicts?: ConflictSchedule[];
  };
}

export const getTherapistWorkingHours = async (
  therapistId: string,
  slotType?: string
): Promise<WorkingHoursResponse> => {
  try {
    const directAxios = axios.create({
      baseURL: process.env.BASE_API_URL as string,
      headers: {
        'Content-Type': 'application/json',
        "ngrok-skip-browser-warning": "3456"
      }
    });

    // Map the slot type to the expected sessionType value
    let sessionType = '';
    if (slotType) {
      // Check if the slot type contains "Introductory" (case insensitive)
      if (slotType.toLowerCase().includes('introductory')) {
        sessionType = 'introductory';
      } else {
        // Default to consultancy for any other slot type
        sessionType = 'consultancy';
      }
    }

    // Add query parameter for session type if determined
    const queryParams = sessionType ? `?sessionType=${sessionType}` : '';

    // Make a direct call to the backend API
    const response = await directAxios.get(`/therapist/all-working-hours/${therapistId}${queryParams}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching therapist working hours:', error);

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as {
        response?: {
          data: unknown;
          status: number;
        };
      };

      if (axiosError.response) {
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
      }
    }
    throw error;
  }
};

