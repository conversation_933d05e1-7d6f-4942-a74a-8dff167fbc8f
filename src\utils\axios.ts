import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from "axios";
import toast from "react-hot-toast";

const authKey =
  typeof window !== "undefined" ? localStorage.getItem("authKeyTh") : null;

// Create a public axios instance that doesn't require authentication
export const publicAxiosInstance: AxiosInstance = axios.create({
  baseURL: process.env.BASE_API_URL as string,
  headers: {
    "Content-Type": "application/json",
    "ngrok-skip-browser-warning": "3456"
  },
});

// Add interceptors to the public axios instance
publicAxiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    console.error('Public API error:', error);
    return Promise.reject(
      (error.response && error.response.data) || "Something went wrong!"
    );
  }
);

// Regular authenticated axios instance
const axiosInstance: AxiosInstance = axios.create({
  baseURL: process.env.BASE_API_URL as string, // Ensure the type is explicitly a string,
  headers: {
    "Content-Type": "application/json",
    Authorization: `${authKey}`,
    "ngrok-skip-browser-warning": "3456"

  },
});

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    // Handle success responses
    const data = response.data;

    // Check if the request URL is related to public calendar
    const url = response.config.url || '';
    const isPublicCalendarEndpoint =
      url.includes('/therapist/onboarding') ||
      url.includes('/therapist/working-hours') ||
      url.includes('/therapist/specific-working-hours');

    // Only show success toast if:
    // 1. It's not a GET request to public calendar endpoints, AND
    // 2. It's not a webhook endpoint (webhook service handles its own toasts), AND
    // 3. It's any request to other endpoints
    // 4. Check for both 'success' and 'status' fields for proper success detection
    if (data && ((data.status === "success" && data.message) || (data.success === true && data.message)) &&
        !(isPublicCalendarEndpoint && response.config.method === "get") &&
        !isWebhookEndpoint) {
      // Show success toast for successful operations with messages
      toast.success(data.message);
    }
    return response;
  },
  (error: AxiosError) => {
    const _status = error?.response?.status;
    switch (_status) {
      case 400:
        toast.error(
          (error?.response?.data as { message?: string })?.message ||
            (error?.response?.data as string)?.toString() ||
            "An error occurred"
        );
        break;
      case 401:
        toast.error("Session Timeout!");
        localStorage.removeItem("authKeyTh");
        window.location.href = "/";
        break;

      default:
        break;
    }
    // if (error.response && error.response?.status === 401) {
    //   toast.error("Session Timeout!");
    //   localStorage.removeItem("authKeyTh");
    //   window.location.href = "/";
    // }
    return Promise.reject(
      (error.response && error.response.data) || "Something went wrong!"
    );
  }
);

export default axiosInstance;

// ----------------------------------------------------------------------------

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  try {
    const [url, config] = Array.isArray(args) ? args : [args];

    const res = await axiosInstance.get(url, { ...config });

    return res.data;
  } catch (error) {
    console.error("Failed to fetch:", error);
    throw error;
  }
};

export const formatTime = (dateStr: string): string => {
  const date = new Date(dateStr);
  // Format the time as HH:MM AM/PM
  return date.toLocaleTimeString(undefined, {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
};
export const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr);

  const day = date.getDate();
  const month = date.toLocaleString("en-US", { month: "short" });
  const year = date.getFullYear();

  return `${day} ${month}, ${year}`;
};
