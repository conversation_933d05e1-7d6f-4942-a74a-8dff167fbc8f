import Login from "@/components/common/Login";
import { useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";

const steps = [
  {
    color: "#4C6FFF",
    title: "Sign Up for Free",
    desc: "No credit card needed. Get instant access.",
  },
  {
    color: "#A6F750",
    title: "Set Up Your Practice",
    desc: "Import your clients, schedule, and customize settings.",
  },
  {
    color: "#D174F7",
    title: "Let Thought Pudding Handle the Rest",
    desc: "Automate admin work while you focus on therapy.",
  },
];

const HowItWorksSection = () => {
  const queryParams = useSearchParams();
  const [loginOpen, setLoginOpen] = useState(false);
  const [th_id, setTherapistId] = useState<string | null>(null);
  const [userStep, setUserStep] = useState<string | null>(null);

  const therapist_id = queryParams.get("q");
  const user_step = queryParams.get("step");
  const loginParam = queryParams.get("login");

  useEffect(() => {
    if (therapist_id && user_step) {
      setTherapistId(therapist_id);
      setUserStep(user_step);
      setLoginOpen(true);
    }
  }, [therapist_id, user_step]);

  useEffect(() => {
    if (loginParam) {
      setLoginOpen(true);
    }
  }, []);
  
  return (
    <section className="w-full py-20 m-0">
      {/* Global Heading */}
      <h2 className="text-2xl md:text-4xl lg:text-[60px] font-semibold text-center mb-14">
        How Thought Pudding <span className="text-[#D174F7]">works</span>?
      </h2>

      <div className="max-w-9xl flex flex-col md:flex-row gap-10 m-0 px-4 sm:px-6 md:px-8">
        {/* Left Column */}
        <div className="md:w-[35%] lg:mr-[50px] lg:pl-16 hidden md:flex flex-col justify-center text-center md:text-left">
          <h3 className="text-[30px] lg:text-[50px] font-bold text-[#4C6FFF] mb-3 w-full">
            5 Min Process
          </h3>

          <p className="text-base sm:text-lg font-semibold text-gray-600 mb-4">
            Get started in minutes
          </p>
          <button onClick={() => setLoginOpen(true)} className="text-[13px] w-[150px] lg:w-[246px] lg:text-[20px] px-3 py-3 bg-[#251D5C] text-white rounded-lg">
            Start Free Trial
          </button>
        </div>

        {/* Right Column */}
        <div className="md:w-[65%] flex items-center lg:pl-32">
          <div className="relative w-full">
            {/* Vertical line */}
            <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-[#2D2D5A] z-0" />

            {/* Steps */}
            <div className="space-y-24 relative z-10">
              {steps.map((step, index) => (
                <div key={index} className="relative pl-16 w-full lg:w-[95%]">
                  {/* Dot on the line */}
                  <div
                    className="absolute left-6 w-8 h-8 rounded-full"
                    style={{
                      backgroundColor: step.color,
                      top: "0.25rem",
                      transform: "translate(-50%, 0)",
                    }}
                  />

                  {/* Step Content */}
                  <h4 className="text-[24px] sm:text-[28px] md:text-[30px] lg:text-[35px] font-semibold mb-1">
                    {step.title}
                  </h4>
                  <p className="text-[10px] md:text-[18px] lg:text-[20px] text-gray-600">
                    {step.desc}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div id="subscription" className="w-full bg-[#718FFF] py-24 px-6 rounded-3xl mt-20">
        <div className="max-w-7xl mx-auto flex flex-col min-[821px]:flex-row items-center justify-between gap-8">
          {/* Left Text Section */}
          <div className="min-[821px]:w-[40%] text-center min-[821px]:text-left text-white max-w-xs">
            <h2 className="text-3xl sm:text-4xl font-bold mb-2 leading-snug">
              Try Thought Pudding <span className="text-white">for free</span>
            </h2>
            <p className="text-lg">No credit card required.</p>
          </div>

          {/* Pricing Cards */}
          <div className="flex flex-col sm:flex-row items-center gap-6 min-[821px]:w-[60%]">
            {/* Monthly Plan */}
            <div className="bg-white rounded-2xl px-6 py-8 w-full sm:w-1/2 text-center shadow-md">
              {/* Plan Label */}
              <div
                className="inline-block px-4 py-1 mb-4 rounded-md text-lg font-bold text-[#111]"
                style={{
                  backgroundImage:
                    "url('/assets/images/newHome/Hero-Title-back.png')",
                  width: "60%",
                  height: "100%",
                  transform: "rotate(0deg)",
                }}
              >
                Beck
              </div>

              <p className="flex items-baseline gap-1 mb-1 justify-center">
                <span className="text-[18px] md:text-[28px] lg:text-[40px] font-bold">
                  ₹300/
                </span>
                <span className="text-[12px] md:text-[16px] lg:text-[20px] font-medium whitespace-nowrap">
                  monthly
                </span>
              </p>

              <p className="text-[10px] md:text-[14px] lg:text-[18px] md:px-6 font-semibold text-gray-700 mb-6 text-center">
              300 INR per month, includes all features from intake to income (incl. GST)
              </p>

              <button onClick={() => setLoginOpen(true)} className="bg-[#251D5C] text-white px-5 py-2 rounded-lg font-semibold w-fit mx-auto">
                Join Now
              </button>
            </div>

            {/* Yearly Plan */}
            <div className="bg-[#251D5C] rounded-2xl px-6 py-8 w-full sm:w-1/2 text-center text-white shadow-md">
              {/* Plan Label */}
              <div
                className="inline-block px-4 py-1 mb-4 rounded-md text-lg font-bold text-[#111]"
                style={{
                  backgroundImage: "url('/assets/images/newHome/Name-bg3.png')",
                  width: "60%",
                  height: "100%",
                  transform: "rotate(0deg)",
                }}
              >
                Freud
              </div>

              <p className="flex items-baseline gap-1 mb-1 justify-center text-white">
                <span className="text-[18px] md:text-[28px] lg:text-[40px] font-bold">
                  ₹800/
                </span>
                <span className="text-[12px] md:text-[16px] lg:text-[20px] font-medium whitespace-nowrap">
                  yearly
                </span>
              </p>

              <p className="text-[10px] md:text-[14px] lg:text-[18px] md:px-6 font-semibold text-white mb-6 text-center">
              15 day free trial + 800 INR for the year (incl. GST) 
              Includes all features and priority application to our Referral Network 
              </p>

              <button onClick={() => setLoginOpen(true)} className="bg-white text-[#251D5C] px-5 py-2 rounded-lg font-semibold w-fit mx-auto">
                Join Now
              </button>
            </div>
          </div>
        </div>
      </div>
      <Login
        setLoginOpen={setLoginOpen}
        loginOpen={loginOpen}
        th_id={th_id}
        userStep={userStep}
      />
    </section>
  );
};

export default HowItWorksSection;
