import React, { useState, useEffect } from "react";
import ReactD<PERSON> from "react-dom";
import Button from "./Button";
import { IoCloseCircleOutline } from "react-icons/io5";
import { Warning, Clock, User, Calendar } from "@phosphor-icons/react";
import moment from "moment";

interface ConflictItem {
  eventId: string;
  conflictType: 'time_overlap' | 'double_booking';
  newSession: {
    fromDate: string;
    toDate: string;
    clientName?: string;
    summary?: string;
  };
  existingSession: {
    fromDate: string;
    toDate: string;
    clientName?: string;
    summary?: string;
  };
  conflictingWith: string[];
  suggestedResolution: string;
}

interface EnhancedConflictModalProps {
  open: boolean;
  onClose: () => void;
  message: string;
  conflicts: ConflictItem[];
  onResolve: (resolutions: Array<{
    eventId: string;
    resolution: 'keep_existing' | 'keep_new' | 'skip' | 'manual_review';
  }>) => void;
}

const EnhancedConflictModal: React.FC<EnhancedConflictModalProps> = ({
  open,
  onClose,
  message,
  conflicts,
  onResolve,
}) => {
  const [container, setContainer] = useState<HTMLDivElement | null>(null);
  const [resolutions, setResolutions] = useState<{[key: string]: string}>({});
  const [isResolving, setIsResolving] = useState(false);

  useEffect(() => {
    if (typeof document !== "undefined") {
      const portalContainer = document.getElementById("__next");
      if (!portalContainer) {
        const newContainer = document.createElement("div");
        newContainer.id = "portal-root";
        document.body.appendChild(newContainer);
        setContainer(newContainer);
      } else {
        setContainer(portalContainer as HTMLDivElement);
      }
    }
  }, []);

  useEffect(() => {
    if (typeof document !== "undefined" && open) {
      document.body.style.overflow = "hidden";
      
      const handleEscKeyPress = (event: KeyboardEvent) => {
        if (event.key === "Escape" && open) {
          onClose();
        }
      };

      document.addEventListener("keydown", handleEscKeyPress);

      return () => {
        document.removeEventListener("keydown", handleEscKeyPress);
        document.body.style.overflow = "";
      };
    }
  }, [open, onClose]);

  const handleResolutionChange = (eventId: string, resolution: string) => {
    setResolutions(prev => ({
      ...prev,
      [eventId]: resolution
    }));
  };

  const handleResolveAll = async () => {
    setIsResolving(true);

    const resolutionArray = conflicts.map(conflict => ({
      eventId: conflict.eventId,
      resolution: (resolutions[conflict.eventId] || 'manual_review') as 'keep_existing' | 'keep_new' | 'skip' | 'manual_review'
    }));

    try {
      await onResolve(resolutionArray);
      onClose();
    } catch (error) {
      console.error("Error resolving conflicts:", error);
    } finally {
      setIsResolving(false);
    }
  };

  const getConflictTypeIcon = (type: string) => {
    switch (type) {
      case 'time_overlap':
        return <Clock className="text-orange-500" size={20} />;
      case 'double_booking':
        return <Calendar className="text-red-500" size={20} />;
      default:
        return <Warning className="text-yellow-500" size={20} />;
    }
  };

  const getConflictTypeText = (type: string) => {
    switch (type) {
      case 'time_overlap':
        return 'Time Overlap';
      case 'double_booking':
        return 'Double Booking';
      default:
        return 'Conflict';
    }
  };

  const formatDateTime = (dateTime: string) => {
    return moment(dateTime).format("MMM DD, YYYY [at] hh:mm A");
  };

  const formatDuration = (fromDate: string, toDate: string) => {
    const start = moment(fromDate);
    const end = moment(toDate);
    const duration = moment.duration(end.diff(start));
    return `${Math.floor(duration.asHours())}h ${duration.minutes()}m`;
  };

  if (!container || !open) {
    return null;
  }

  return ReactDOM.createPortal(
    <div
      className={`fixed inset-0 z-[99999] flex items-center justify-center bg-gray-800 bg-opacity-50 transition-opacity duration-300 ${
        open ? "opacity-100" : "opacity-0 pointer-events-none"
      }`}
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg shadow-xl transform transition-transform duration-300 overflow-y-auto mx-4 my-6 w-full max-w-4xl max-h-[90vh]"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Warning className="text-orange-500" size={24} />
            <h2 className="text-xl font-semibold text-gray-900">
              Schedule Conflicts Detected
            </h2>
          </div>
          <button
            className="text-gray-400 hover:text-gray-600 transition-colors"
            onClick={onClose}
            aria-label="Close modal"
          >
            <IoCloseCircleOutline className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="mb-6">
            <p className="text-gray-700 mb-2">{message}</p>
            <p className="text-sm text-gray-500">
              Please review each conflict and choose how to resolve it. You can keep the existing session, 
              use the new session time, or skip the conflicting session entirely.
            </p>
          </div>

          {/* Conflicts List */}
          <div className="space-y-6">
            {conflicts.map((conflict, index) => (
              <div key={conflict.eventId} className="conflict-item border border-orange-200 bg-orange-50 rounded-lg p-4">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    {getConflictTypeIcon(conflict.conflictType)}
                    <h3 className="font-medium text-gray-900">
                      Conflict #{index + 1}: {getConflictTypeText(conflict.conflictType)}
                    </h3>
                  </div>
                  <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded">
                    Event ID: {conflict.eventId.slice(-8)}
                  </span>
                </div>

                {/* Conflict Comparison */}
                <div className="conflict-comparison grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {/* Existing Session */}
                  <div className="bg-white border border-gray-200 rounded-md p-4">
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                      <Calendar className="mr-2" size={16} />
                      Existing Session
                    </h4>
                    <div className="space-y-2 text-sm">
                      <p className="flex items-center text-gray-600">
                        <Clock className="mr-2" size={14} />
                        {formatDateTime(conflict.existingSession.fromDate)}
                      </p>
                      <p className="text-gray-500">
                        Duration: {formatDuration(conflict.existingSession.fromDate, conflict.existingSession.toDate)}
                      </p>
                      {conflict.existingSession.clientName && (
                        <p className="flex items-center text-gray-600">
                          <User className="mr-2" size={14} />
                          {conflict.existingSession.clientName}
                        </p>
                      )}
                      {conflict.existingSession.summary && (
                        <p className="text-gray-600 text-xs">
                          {conflict.existingSession.summary}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* New Session */}
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                      <Calendar className="mr-2" size={16} />
                      New Session
                    </h4>
                    <div className="space-y-2 text-sm">
                      <p className="flex items-center text-gray-600">
                        <Clock className="mr-2" size={14} />
                        {formatDateTime(conflict.newSession.fromDate)}
                      </p>
                      <p className="text-gray-500">
                        Duration: {formatDuration(conflict.newSession.fromDate, conflict.newSession.toDate)}
                      </p>
                      {conflict.newSession.clientName && (
                        <p className="flex items-center text-gray-600">
                          <User className="mr-2" size={14} />
                          {conflict.newSession.clientName}
                        </p>
                      )}
                      {conflict.newSession.summary && (
                        <p className="text-gray-600 text-xs">
                          {conflict.newSession.summary}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Resolution Options */}
                <div className="resolution-actions">
                  <h5 className="font-medium text-gray-900 mb-3">Choose Resolution:</h5>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2">
                    {[
                      { value: 'keep_existing', label: 'Keep Existing', desc: 'Maintain current session' },
                      { value: 'keep_new', label: 'Use New Time', desc: 'Replace with new session' },
                      { value: 'skip', label: 'Skip Session', desc: 'Skip this conflicting session' },
                      { value: 'manual_review', label: 'Manual Review', desc: 'Resolve later manually' }
                    ].map((option) => (
                      <label key={option.value} className="flex items-start space-x-2 cursor-pointer p-2 border rounded hover:bg-gray-50">
                        <input
                          type="radio"
                          name={`resolution-${conflict.eventId}`}
                          value={option.value}
                          checked={resolutions[conflict.eventId] === option.value}
                          onChange={(e) => handleResolutionChange(conflict.eventId, e.target.value)}
                          className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{option.label}</div>
                          <div className="text-xs text-gray-500">{option.desc}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Suggested Resolution */}
                {conflict.suggestedResolution && (
                  <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <p className="text-sm text-blue-800">
                      <strong>Suggestion:</strong> {conflict.suggestedResolution}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {conflicts.length} conflict{conflicts.length > 1 ? 's' : ''} found
          </div>
          <div className="flex space-x-3">
            <Button
              onClick={onClose}
              variant="outlinedGray"
              disabled={isResolving}
            >
              Cancel
            </Button>
            <Button
              onClick={handleResolveAll}
              variant="filledGreen"
              disabled={isResolving || Object.keys(resolutions).length !== conflicts.length}
            >
              {isResolving ? "Resolving..." : "Resolve All Conflicts"}
            </Button>
          </div>
        </div>
      </div>
    </div>,
    container
  );
};

export default EnhancedConflictModal;
